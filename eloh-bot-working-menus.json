{"name": "ELOH Processing Bot - Working Menu System", "nodes": [{"parameters": {"updates": ["message", "chat_member", "callback_query", "pre_checkout_query"], "additionalFields": {}}, "id": "telegram-trigger", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [272, -1840], "webhookId": "76bfb192-83ad-4541-9197-36d47cf7049b", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "const message = $json.message;\nconst callbackQuery = $json.callback_query;\nconst chatMember = $json.chat_member;\n\nlet userId = (message && message.from && message.from.id) || (callbackQuery && callbackQuery.from && callbackQuery.from.id) || (chatMember && chatMember.new_chat_member && chatMember.new_chat_member.user && chatMember.new_chat_member.user.id);\n\nlet chatId = (message && message.chat && message.chat.id) || (callbackQuery && callbackQuery.message && callbackQuery.message.chat && callbackQuery.message.chat.id) || (chatMember && chatMember.chat && chatMember.chat.id);\n\nlet username = (message && message.from && message.from.username) || (callbackQuery && callbackQuery.from && callbackQuery.from.username) || (chatMember && chatMember.new_chat_member && chatMember.new_chat_member.user && chatMember.new_chat_member.user.username);\n\nlet firstName = (message && message.from && message.from.first_name) || (callbackQuery && callbackQuery.from && callbackQuery.from.first_name) || (chatMember && chatMember.new_chat_member && chatMember.new_chat_member.user && chatMember.new_chat_member.user.first_name);\n\nlet isCallback = !!callbackQuery;\n\nreturn {\n  json: {\n    ...$json,\n    unifiedUserId: userId,\n    unifiedChatId: chatId,\n    unifiedUsername: username,\n    unifiedFirstName: firstName,\n    isCallback: isCallback\n  }\n};"}, "id": "standardize-user-data", "name": "Standardize User Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [416, -1840]}, {"parameters": {"operation": "getAll", "tableId": "users", "returnAll": true, "filters": {"conditions": [{"keyName": "telegram_id", "condition": "eq", "keyValue": "={{ $json.unifiedUserId.toString() }}"}]}}, "id": "check-existing-user", "name": "Check Existing User", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [560, -1840], "alwaysOutputData": true, "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "user-exists-check", "leftValue": "={{ $json.length }}", "rightValue": 0, "operator": {"type": "number", "operation": "gt"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [704, -1840], "id": "user-check", "name": "User Check"}, {"parameters": {"jsCode": "// Mark as existing user\nconst originalData = $('Standardize User Data').item.json;\nconst supabaseUser = $json[0] || $json;\n\nreturn {\n  json: {\n    ...originalData,\n    user: supabaseUser,\n    is_new_user: false,\n    is_guest: false\n  }\n};"}, "id": "mark-existing-user", "name": "<PERSON> Existing User", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [848, -1920]}, {"parameters": {"jsCode": "// Create guest user for browsing\nconst originalData = $('Standardize User Data').item.json;\n\nconst guestUser = {\n  telegram_id: originalData.unifiedUserId,\n  username: originalData.unifiedUsername || '',\n  name: originalData.unifiedFirstName || 'Guest',\n  role: 'guest',\n  is_verified_investor: false\n};\n\nreturn {\n  json: {\n    ...originalData,\n    user: guestUser,\n    is_new_user: false,\n    is_guest: true\n  }\n};"}, "id": "guest-mode-handler", "name": "Guest <PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [848, -1760]}, {"parameters": {"dataType": "string", "value1": "={{ $json.message ? ($json.message.text.includes(' ') ? $json.message.text.split(' ')[0] : $json.message.text) : ($json.callback_query ? 'callback' : 'unknown') }}", "rules": {"rules": [{"value2": "/start"}, {"value2": "callback", "output": 1}]}}, "id": "main-switch", "name": "Main Switch", "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [992, -1840]}, {"parameters": {"jsCode": "// Smart Menu Builder - Build main menu based on user type\nconst user = $json.user;\nconst isGuest = $json.is_guest || user?.role === 'guest';\nconst chatId = $json.unifiedChatId;\n\nlet welcomeText;\nlet keyboard;\n\nif (isGuest) {\n  welcomeText = `👋 *Welcome to ELOH Processing DAO*\\n\\n*🏭 Sustainable Crypto Mining in Dominica*\\n\\n🔓 **Browsing as Guest** - Limited Access`;\n  \n  keyboard = [\n    [\n      { text: \"🗺️ View Public Roadmap\", callback_data: \"show_roadmap_public\" },\n      { text: \"📊 Live Mining Stats\", callback_data: \"guest_mining_stats\" }\n    ],\n    [\n      { text: \"🔧 Browse Services (Guest)\", callback_data: \"services_menu\" },\n      { text: \"💝 Make Donation\", callback_data: \"donate\" }\n    ],\n    [\n      { text: \"💰 Investment Info\", callback_data: \"guest_investment_info\" },\n      { text: \"✅ Create Free Account\", callback_data: \"register_user\" }\n    ]\n  ];\n} else {\n  welcomeText = `🚀 *Welcome to ELOH Processing DAO*\\n\\n*🏭 Sustainable Crypto Mining in Dominica*\\n\\nHello ${user?.name || 'User'}!`;\n  \n  keyboard = [\n    [\n      { text: \"🗺️ Public Roadmap\", callback_data: \"show_roadmap_public\" },\n      { text: \"💰 My Investment\", callback_data: \"view_investment\" }\n    ],\n    [\n      { text: \"🔧 Our Services\", callback_data: \"services_menu\" },\n      { text: \"💝 Donate\", callback_data: \"donate\" }\n    ]\n  ];\n}\n\nreturn {\n  chatId: chatId,\n  text: welcomeText,\n  reply_markup: {\n    inline_keyboard: keyboard\n  },\n  parse_mode: \"Markdown\",\n  messageId: $json.isCallback ? $json.callback_query?.message?.message_id : undefined\n};"}, "id": "build-main-menu", "name": "Build Main Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1136, -1920]}, {"parameters": {"chatId": "={{ $json.chatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "additionalFields": {}}, "id": "send-main-menu", "name": "Send Main Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1280, -1920], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"dataType": "string", "value1": "={{ $json.callback_query ? $json.callback_query.data : 'no_callback' }}", "rules": {"rules": [{"value2": "main_menu"}, {"value2": "services_menu", "output": 1}, {"value2": "donate", "output": 2}, {"value2": "register_user", "output": 3}, {"value2": "view_investment", "output": 4}, {"value2": "show_roadmap_public", "output": 5}, {"value2": "guest_mining_stats", "output": 6}, {"value2": "guest_investment_info", "output": 7}]}}, "id": "callback-router", "name": "Callback Router", "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [1136, -1760]}, {"parameters": {"jsCode": "// Services Menu Builder\nconst user = $json.user;\nconst isGuest = $json.is_guest || user?.role === 'guest';\n\nlet headerText;\nlet serviceButtons;\n\nif (isGuest) {\n  headerText = `🔧 *ELOH Processing Services*\\n\\n🔓 **Browsing as Guest** - Registration required for purchases`;\n  serviceButtons = [\n    [{ text: \"🔍 Mining Services - $500/mo (View Details)\", callback_data: \"guest_service_mining\" }],\n    [{ text: \"🔍 Pool Membership - $200/yr (View Details)\", callback_data: \"guest_service_pool\" }],\n    [{ text: \"✅ Register to Purchase\", callback_data: \"register_user\" }]\n  ];\n} else {\n  headerText = `🔧 *ELOH Processing Services*\\n\\nLeverage our expertise:`;\n  serviceButtons = [\n    [{ text: \"💳 Mining Services - $500/mo\", callback_data: \"order_mining_500\" }],\n    [{ text: \"💳 Pool Membership - $200/yr\", callback_data: \"order_pool_200\" }]\n  ];\n}\n\nconst keyboard = [...serviceButtons, [\n  { text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }\n]];\n\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `${headerText}\\n\\nSelect a service below:`,\n  reply_markup: {\n    inline_keyboard: keyboard\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.callback_query?.message?.message_id\n};"}, "id": "build-services-menu", "name": "Build Services Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1280, -1760]}, {"parameters": {"chatId": "={{ $json.chatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "additionalFields": {}}, "id": "send-services-menu", "name": "Send Services Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1424, -1760], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "// Donate Menu Builder\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `💝 *Support ELOH Processing DAO*\\n\\nYour contributions help maintain our sustainable mining operations.\\n\\nChoose an amount:`,\n  reply_markup: {\n    inline_keyboard: [\n      [{ text: \"$5 - Support ELOH\", callback_data: \"donate_5\" }],\n      [{ text: \"$10 - Fuel Operations\", callback_data: \"donate_10\" }],\n      [{ text: \"$25 - Expand Infrastructure\", callback_data: \"donate_25\" }],\n      [{ text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }]\n    ]\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.callback_query?.message?.message_id\n};"}, "id": "build-donate-menu", "name": "Build Donate Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1280, -1600]}, {"parameters": {"chatId": "={{ $json.chatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "additionalFields": {}}, "id": "send-donate-menu", "name": "Send Donate <PERSON>u", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1424, -1600], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "// Registration Handler\nconst user = $json.user;\nconst isGuest = $json.is_guest || user?.role === 'guest';\n\nif (!isGuest) {\n  return {\n    chatId: $json.unifiedChatId,\n    text: \"✅ You're already registered!\",\n    reply_markup: {\n      inline_keyboard: [[\n        { text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }\n      ]]\n    },\n    parse_mode: 'Markdown',\n    messageId: $json.callback_query?.message?.message_id\n  };\n}\n\nreturn {\n  chatId: $json.unifiedChatId,\n  text: \"📝 *Account Registration*\\n\\nCreate your account using your Telegram information.\\n\\n**Benefits:**\\n✅ Access investment features\\n💎 Portfolio tracking\\n🔧 Full service access\\n\\nProceed?\",\n  reply_markup: {\n    inline_keyboard: [\n      [{ text: \"✅ Create Account\", callback_data: \"confirm_registration\" }],\n      [{ text: \"❌ Cancel\", callback_data: \"main_menu\" }]\n    ]\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.callback_query?.message?.message_id\n};"}, "id": "registration-handler", "name": "Registration Handler", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1280, -1440]}, {"parameters": {"chatId": "={{ $json.chatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "additionalFields": {}}, "id": "send-registration-response", "name": "Send Registration Response", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1424, -1440], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "=Hello {{ $json.user?.name || 'Investor' }}! Your current investment value is: ${{ $json.user?.investment_details?.total_value_usd || '0' }}.", "additionalFields": {}}, "id": "send-investment-details", "name": "Send Investment Details", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1280, -1280], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "get", "tableId": "project_roadmap", "filters": {"conditions": []}}, "id": "get-roadmap-data", "name": "Get Roadmap Data", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1280, -1120], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"jsCode": "const items = $input.all();\nlet formattedText = '🗺️ **ELOH Processing Public Roadmap**\\n\\n';\nfor (const item of items) {\n  formattedText += `- **${item.json.name}**: ${item.json.status}\\n`;\n}\n\nreturn {\n  chatId: $('Standardize User Data').item.json.unifiedChatId,\n  text: formattedText,\n  reply_markup: {\n    inline_keyboard: [[\n      { text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }\n    ]]\n  },\n  parse_mode: 'Markdown'\n};"}, "id": "format-roadmap-text", "name": "Format Roadmap Text", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1424, -1120]}, {"parameters": {"chatId": "={{ $json.chatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "additionalFields": {}}, "id": "send-roadmap", "name": "Send Roadmap", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1568, -1120], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "// Guest Mining Stats\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `📊 *ELOH Processing - Live Mining Stats*\\n\\n🔓 **Public Statistics** (Guest View)\\n\\n⚡ **Current Operations:**\\n• Active Miners: 24 ASIC units\\n• Hash Rate: ~2.4 PH/s\\n• Power: 100% renewable\\n• Uptime: 99.2%\\n\\n🔐 *Register for detailed analytics*`,\n  reply_markup: {\n    inline_keyboard: [\n      [{ text: \"✅ Register for Full Access\", callback_data: \"register_user\" }],\n      [{ text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }]\n    ]\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.callback_query?.message?.message_id\n};"}, "id": "guest-mining-stats", "name": "Guest Mining Stats", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1280, -960]}, {"parameters": {"chatId": "={{ $json.chatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "additionalFields": {}}, "id": "send-guest-mining-stats", "name": "Send Guest Mining Stats", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1424, -960], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "// Guest Investment Info\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `💎 *Why Invest with ELOH Processing?*\\n\\n🔓 **Guest Information**\\n\\n🏭 **Our Mission:**\\nSustainable cryptocurrency mining using 100% renewable energy.\\n\\n💰 **Investment Highlights:**\\n• Minimum Investment: $1,000\\n• Expected ROI: 15-25% annually\\n• Monthly dividends\\n• Full transparency\\n\\n🔐 *Register to access detailed investment materials*`,\n  reply_markup: {\n    inline_keyboard: [\n      [{ text: \"✅ Register to Invest\", callback_data: \"register_user\" }],\n      [{ text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }]\n    ]\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.callback_query?.message?.message_id\n};"}, "id": "guest-investment-info", "name": "Guest Investment Info", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1280, -800]}, {"parameters": {"chatId": "={{ $json.chatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "additionalFields": {}}, "id": "send-guest-investment-info", "name": "Send Guest Investment Info", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1424, -800], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}], "connections": {"Telegram Trigger": {"main": [[{"node": "Standardize User Data", "type": "main", "index": 0}]]}, "Standardize User Data": {"main": [[{"node": "Check Existing User", "type": "main", "index": 0}]]}, "Check Existing User": {"main": [[{"node": "User Check", "type": "main", "index": 0}]]}, "User Check": {"main": [[{"node": "<PERSON> Existing User", "type": "main", "index": 0}], [{"node": "Guest <PERSON>", "type": "main", "index": 0}]]}, "Mark Existing User": {"main": [[{"node": "Main Switch", "type": "main", "index": 0}]]}, "Guest Mode Handler": {"main": [[{"node": "Main Switch", "type": "main", "index": 0}]]}, "Main Switch": {"main": [[{"node": "Build Main Menu", "type": "main", "index": 0}], [{"node": "Callback Router", "type": "main", "index": 0}]]}, "Build Main Menu": {"main": [[{"node": "Send Main Menu", "type": "main", "index": 0}]]}, "Callback Router": {"main": [[{"node": "Build Main Menu", "type": "main", "index": 0}], [{"node": "Build Services Menu", "type": "main", "index": 0}], [{"node": "Build Donate Menu", "type": "main", "index": 0}], [{"node": "Registration Handler", "type": "main", "index": 0}], [{"node": "Send Investment Details", "type": "main", "index": 0}], [{"node": "Get Roadmap Data", "type": "main", "index": 0}], [{"node": "Guest Mining Stats", "type": "main", "index": 0}], [{"node": "Guest Investment Info", "type": "main", "index": 0}]]}, "Build Services Menu": {"main": [[{"node": "Send Services Menu", "type": "main", "index": 0}]]}, "Build Donate Menu": {"main": [[{"node": "Send Donate <PERSON>u", "type": "main", "index": 0}]]}, "Registration Handler": {"main": [[{"node": "Send Registration Response", "type": "main", "index": 0}]]}, "Get Roadmap Data": {"main": [[{"node": "Format Roadmap Text", "type": "main", "index": 0}]]}, "Format Roadmap Text": {"main": [[{"node": "Send Roadmap", "type": "main", "index": 0}]]}, "Guest Mining Stats": {"main": [[{"node": "Send Guest Mining Stats", "type": "main", "index": 0}]]}, "Guest Investment Info": {"main": [[{"node": "Send Guest Investment Info", "type": "main", "index": 0}]]}}}