-- Enhanced ELOH Processing Bot Database Schema
-- Optimized for n8n workflow integration

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (enhanced)
CREATE TABLE public.users (
  user_id uuid NOT NULL DEFAULT uuid_generate_v4(),
  telegram_id text UNIQUE NOT NULL,
  username text,
  name character varying,
  is_verified_investor boolean DEFAULT false,
  wallet_address character varying,
  investment_details jsonb DEFAULT '{}',
  role text DEFAULT 'investor' CHECK (role = ANY (ARRAY['admin', 'investor', 'vendor'])),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT users_pkey PRIMARY KEY (user_id)
);

-- Vendors table
CREATE TABLE public.vendors (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  name text NOT NULL,
  description text,
  contact_info jsonb DEFAULT '{}',
  website_url text,
  image_url text,
  is_active boolean NOT NULL DEFAULT true,
  owner_user_id uuid,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT vendors_pkey PRIMARY KEY (id),
  CONSTRAINT vendors_owner_user_id_fkey FOREIGN KEY (owner_user_id) REFERENCES public.users(user_id)
);

-- Menus table (service categories per vendor)
CREATE TABLE public.menus (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  vendor_id uuid NOT NULL,
  name text NOT NULL,
  description text,
  order_index integer DEFAULT 0,
  is_active boolean NOT NULL DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT menus_pkey PRIMARY KEY (id),
  CONSTRAINT menus_vendor_id_fkey FOREIGN KEY (vendor_id) REFERENCES public.vendors(id) ON DELETE CASCADE
);

-- Menu items table (individual services/products)
CREATE TABLE public.menu_items (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  menu_id uuid NOT NULL,
  name text NOT NULL,
  description text,
  price numeric NOT NULL CHECK (price >= 0),
  category text NOT NULL,
  image_url text,
  is_available boolean NOT NULL DEFAULT true,
  order_index integer DEFAULT 0,
  allergens text[],
  preparation_time integer, -- in minutes
  metadata jsonb DEFAULT '{}', -- for additional service-specific data
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT menu_items_pkey PRIMARY KEY (id),
  CONSTRAINT menu_items_menu_id_fkey FOREIGN KEY (menu_id) REFERENCES public.menus(id) ON DELETE CASCADE
);

-- Carts table (user shopping carts)
CREATE TABLE public.carts (
  user_id text NOT NULL,
  vendor_id uuid,
  items jsonb NOT NULL DEFAULT '[]',
  session_data jsonb DEFAULT '{}',
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT carts_pkey PRIMARY KEY (user_id),
  CONSTRAINT carts_vendor_id_fkey FOREIGN KEY (vendor_id) REFERENCES public.vendors(id)
);

-- Orders table (completed purchases)
CREATE TABLE public.orders (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id text NOT NULL,
  chat_id text NOT NULL,
  vendor_id uuid,
  items jsonb NOT NULL DEFAULT '[]',
  total_amount numeric NOT NULL CHECK (total_amount >= 0),
  currency text NOT NULL DEFAULT 'USD',
  payment_status text NOT NULL DEFAULT 'pending' CHECK (payment_status = ANY (ARRAY['pending', 'paid', 'failed', 'cancelled', 'refunded'])),
  delivery_type text CHECK (delivery_type = ANY (ARRAY['pickup', 'delivery', 'digital'])),
  order_status text NOT NULL DEFAULT 'new' CHECK (order_status = ANY (ARRAY['new', 'confirmed', 'preparing', 'ready_for_pickup', 'out_for_delivery', 'completed', 'cancelled'])),
  payment_payload text UNIQUE,
  shipping_address jsonb DEFAULT '{}',
  telegram_charge_id text,
  provider_charge_id text,
  estimated_delivery timestamp with time zone,
  special_instructions text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT orders_pkey PRIMARY KEY (id),
  CONSTRAINT orders_vendor_id_fkey FOREIGN KEY (vendor_id) REFERENCES public.vendors(id)
);

-- User deposits table (payment tracking)
CREATE TABLE public.user_deposits (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  amount numeric NOT NULL CHECK (amount > 0),
  currency character varying DEFAULT 'USD',
  deposit_type text DEFAULT 'donation' CHECK (deposit_type = ANY (ARRAY['donation', 'investment', 'service_payment', 'order_payment'])),
  date timestamp with time zone DEFAULT now(),
  tx_hash character varying,
  telegram_payment_id character varying,
  order_id uuid, -- link to orders if applicable
  status text DEFAULT 'completed' CHECK (status = ANY (ARRAY['pending', 'completed', 'failed', 'refunded'])),
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_deposits_pkey PRIMARY KEY (id),
  CONSTRAINT user_deposits_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(user_id),
  CONSTRAINT user_deposits_order_id_fkey FOREIGN KEY (order_id) REFERENCES public.orders(id)
);

-- Bot menu configuration (dynamic menu system)
CREATE TABLE public.bot_menu (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  menu_type character varying NOT NULL, -- 'main', 'admin', 'vendor', etc.
  topic_id character varying,
  button_text character varying NOT NULL,
  button_type character varying NOT NULL, -- 'callback', 'url', 'payment'
  button_data character varying NOT NULL, -- callback_data or URL
  description text,
  is_active boolean DEFAULT true,
  requires_verification boolean DEFAULT false,
  admin_only boolean DEFAULT false,
  order_index integer DEFAULT 0,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT bot_menu_pkey PRIMARY KEY (id)
);

-- Menu responses (automated responses for bot interactions)
CREATE TABLE public.menu_responses (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  callback_data character varying NOT NULL UNIQUE,
  response_type character varying NOT NULL, -- 'text', 'menu', 'payment', 'action'
  response_content jsonb NOT NULL,
  requires_verification boolean DEFAULT false,
  admin_only boolean DEFAULT false,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT menu_responses_pkey PRIMARY KEY (id)
);

-- Project roadmap (public information)
CREATE TABLE public.project_roadmap (
  id character varying NOT NULL,
  name character varying NOT NULL,
  status character varying NOT NULL,
  details text,
  order_index integer DEFAULT 0,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT project_roadmap_pkey PRIMARY KEY (id)
);

-- Group configuration (bot settings)
CREATE TABLE public.group_config (
  id serial PRIMARY KEY,
  investments_topic_id text,
  roadmap_topic_id text,
  bot_settings jsonb DEFAULT '{}',
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- Indexes for performance
CREATE INDEX idx_users_telegram_id ON public.users(telegram_id);
CREATE INDEX idx_orders_user_id ON public.orders(user_id);
CREATE INDEX idx_orders_status ON public.orders(order_status);
CREATE INDEX idx_orders_payment_status ON public.orders(payment_status);
CREATE INDEX idx_menu_items_menu_id ON public.menu_items(menu_id);
CREATE INDEX idx_menu_items_available ON public.menu_items(is_available);
CREATE INDEX idx_vendors_active ON public.vendors(is_active);
CREATE INDEX idx_bot_menu_type ON public.bot_menu(menu_type);
CREATE INDEX idx_bot_menu_active ON public.bot_menu(is_active);
CREATE INDEX idx_user_deposits_user_id ON public.user_deposits(user_id);
CREATE INDEX idx_user_deposits_order_id ON public.user_deposits(order_id);

-- Functions for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_vendors_updated_at BEFORE UPDATE ON public.vendors FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_menus_updated_at BEFORE UPDATE ON public.menus FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_menu_items_updated_at BEFORE UPDATE ON public.menu_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_carts_updated_at BEFORE UPDATE ON public.carts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON public.orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_bot_menu_updated_at BEFORE UPDATE ON public.bot_menu FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_menu_responses_updated_at BEFORE UPDATE ON public.menu_responses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_project_roadmap_updated_at BEFORE UPDATE ON public.project_roadmap FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_group_config_updated_at BEFORE UPDATE ON public.group_config FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
