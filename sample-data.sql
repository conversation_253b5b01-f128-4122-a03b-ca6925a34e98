-- Sample data for ELOH Processing Bot Enhanced Schema
-- This provides initial data to test the workflow

-- Insert sample project roadmap
INSERT INTO public.project_roadmap (id, name, status, details, order_index) VALUES
('phase-1', 'Infrastructure Setup', 'Completed', 'Basic mining infrastructure established in Dominica', 1),
('phase-2', 'Mining Pool Launch', 'In Progress', 'Launching transparent mining pool for community', 2),
('phase-3', 'DAO Governance', 'Planning', 'Implementing decentralized governance structure', 3),
('phase-4', 'Expansion', 'Future', 'Scaling operations across Caribbean region', 4);

-- Insert sample vendors
INSERT INTO public.vendors (id, name, description, contact_info, is_active) VALUES
(uuid_generate_v4(), 'ELOH Mining Services', 'Professional crypto mining operations and consulting', '{"email": "<EMAIL>", "phone": "******-XXX-XXXX"}', true),
(uuid_generate_v4(), 'ELOH Analytics', 'Market analysis and investment consulting services', '{"email": "<EMAIL>", "phone": "******-XXX-XXXY"}', true),
(uuid_generate_v4(), 'ELOH Infrastructure', 'Hardware and infrastructure management', '{"email": "<EMAIL>", "phone": "******-XXX-XXXZ"}', true);

-- Insert sample menus for vendors
WITH vendor_mining AS (SELECT id FROM public.vendors WHERE name = 'ELOH Mining Services' LIMIT 1),
     vendor_analytics AS (SELECT id FROM public.vendors WHERE name = 'ELOH Analytics' LIMIT 1),
     vendor_infra AS (SELECT id FROM public.vendors WHERE name = 'ELOH Infrastructure' LIMIT 1)
INSERT INTO public.menus (id, vendor_id, name, description, order_index) VALUES
(uuid_generate_v4(), (SELECT id FROM vendor_mining), 'Mining Operations', 'Professional mining services', 1),
(uuid_generate_v4(), (SELECT id FROM vendor_mining), 'Pool Membership', 'Join our mining pool', 2),
(uuid_generate_v4(), (SELECT id FROM vendor_analytics), 'Market Analysis', 'Crypto and forex market insights', 1),
(uuid_generate_v4(), (SELECT id FROM vendor_analytics), 'Investment Consulting', 'Personalized investment advice', 2),
(uuid_generate_v4(), (SELECT id FROM vendor_infra), 'Hardware Setup', 'Mining hardware configuration', 1),
(uuid_generate_v4(), (SELECT id FROM vendor_infra), 'Maintenance', 'Ongoing infrastructure maintenance', 2);

-- Insert sample menu items
WITH menu_mining_ops AS (SELECT id FROM public.menus WHERE name = 'Mining Operations' LIMIT 1),
     menu_pool AS (SELECT id FROM public.menus WHERE name = 'Pool Membership' LIMIT 1),
     menu_analysis AS (SELECT id FROM public.menus WHERE name = 'Market Analysis' LIMIT 1),
     menu_consulting AS (SELECT id FROM public.menus WHERE name = 'Investment Consulting' LIMIT 1),
     menu_hardware AS (SELECT id FROM public.menus WHERE name = 'Hardware Setup' LIMIT 1),
     menu_maintenance AS (SELECT id FROM public.menus WHERE name = 'Maintenance' LIMIT 1)
INSERT INTO public.menu_items (id, menu_id, name, description, price, category, preparation_time) VALUES
-- Mining Operations
(uuid_generate_v4(), (SELECT id FROM menu_mining_ops), 'Basic Mining Package', '24/7 ASIC mining with 1 TH/s capacity', 500.00, 'mining', 1440),
(uuid_generate_v4(), (SELECT id FROM menu_mining_ops), 'Premium Mining Package', '24/7 ASIC mining with 5 TH/s capacity', 2000.00, 'mining', 1440),
(uuid_generate_v4(), (SELECT id FROM menu_mining_ops), 'Enterprise Mining Package', '24/7 ASIC mining with 10 TH/s capacity', 3500.00, 'mining', 2880),

-- Pool Membership
(uuid_generate_v4(), (SELECT id FROM menu_pool), 'Annual Pool Membership', 'Join our transparent mining pool for 1 year', 200.00, 'membership', 60),
(uuid_generate_v4(), (SELECT id FROM menu_pool), 'Lifetime Pool Membership', 'Lifetime access to our mining pool', 1000.00, 'membership', 60),

-- Market Analysis
(uuid_generate_v4(), (SELECT id FROM menu_analysis), 'Weekly Market Report', 'Detailed weekly crypto market analysis', 99.00, 'analysis', 10080),
(uuid_generate_v4(), (SELECT id FROM menu_analysis), 'Custom Market Analysis', 'Personalized market analysis for specific assets', 299.00, 'analysis', 4320),

-- Investment Consulting
(uuid_generate_v4(), (SELECT id FROM menu_consulting), 'Strategy Consultation (1 hour)', 'One-on-one investment strategy session', 150.00, 'consulting', 60),
(uuid_generate_v4(), (SELECT id FROM menu_consulting), 'Portfolio Review', 'Comprehensive portfolio analysis and recommendations', 500.00, 'consulting', 2880),

-- Hardware Setup
(uuid_generate_v4(), (SELECT id FROM menu_hardware), 'ASIC Miner Setup', 'Professional setup and configuration of ASIC miners', 300.00, 'hardware', 480),
(uuid_generate_v4(), (SELECT id FROM menu_hardware), 'Mining Farm Design', 'Complete mining farm planning and setup', 2000.00, 'hardware', 10080),

-- Maintenance
(uuid_generate_v4(), (SELECT id FROM menu_maintenance), 'Monthly Maintenance', 'Regular maintenance and monitoring', 100.00, 'maintenance', 480),
(uuid_generate_v4(), (SELECT id FROM menu_maintenance), 'Emergency Repair', '24/7 emergency hardware repair service', 250.00, 'maintenance', 240);

-- Insert sample bot menu configuration
INSERT INTO public.bot_menu (id, menu_type, button_text, button_type, button_data, description, order_index, requires_verification, admin_only) VALUES
(uuid_generate_v4(), 'main', '🗺️ Public Roadmap', 'callback', 'show_roadmap_public', 'View project roadmap', 1, false, false),
(uuid_generate_v4(), 'main', '💰 My Investment', 'callback', 'view_investment', 'Check investment status', 2, true, false),
(uuid_generate_v4(), 'main', '🛒 Browse Services', 'callback', 'browse_vendors', 'Browse available services', 3, false, false),
(uuid_generate_v4(), 'main', '🛍️ My Cart', 'callback', 'view_cart', 'View shopping cart', 4, false, false),
(uuid_generate_v4(), 'main', '📋 My Orders', 'callback', 'view_orders', 'View order history', 5, false, false),
(uuid_generate_v4(), 'main', '💝 Donate', 'callback', 'donate', 'Support the project', 6, false, false),
(uuid_generate_v4(), 'main', '⚙️ Admin Panel', 'callback', 'admin_panel', 'Administrative functions', 7, false, true),
(uuid_generate_v4(), 'main', '🏭 Operations', 'url', 'https://elohprocessing.site/operations.php', 'View operations page', 8, false, false),
(uuid_generate_v4(), 'main', '📞 Contact', 'url', 'https://elohprocessing.site/contact.php', 'Contact information', 9, false, false);

-- Insert sample menu responses
INSERT INTO public.menu_responses (id, callback_data, response_type, response_content, requires_verification, admin_only) VALUES
(uuid_generate_v4(), 'show_roadmap_public', 'action', '{"action": "fetch_roadmap", "table": "project_roadmap"}', false, false),
(uuid_generate_v4(), 'view_investment', 'action', '{"action": "check_investment", "requires_user": true}', true, false),
(uuid_generate_v4(), 'browse_vendors', 'action', '{"action": "list_vendors", "table": "vendors", "filter": {"is_active": true}}', false, false),
(uuid_generate_v4(), 'view_cart', 'action', '{"action": "show_cart", "table": "carts"}', false, false),
(uuid_generate_v4(), 'view_orders', 'action', '{"action": "list_orders", "table": "orders"}', false, false),
(uuid_generate_v4(), 'donate', 'menu', '{"type": "donation_menu", "amounts": [5, 10, 25, 50, 100]}', false, false),
(uuid_generate_v4(), 'admin_panel', 'menu', '{"type": "admin_menu", "options": ["user_management", "order_management", "analytics"]}', false, true);

-- Insert sample group configuration
INSERT INTO public.group_config (investments_topic_id, roadmap_topic_id, bot_settings) VALUES
('12345', '67890', '{"welcome_message": "Welcome to ELOH Processing DAO!", "auto_verify": false, "payment_methods": ["telegram", "crypto"]}');

-- Create a sample admin user (you'll need to update the telegram_id)
INSERT INTO public.users (telegram_id, username, name, role, is_verified_investor) VALUES
('123456789', 'admin_user', 'Admin User', 'admin', true);

-- Create a sample regular user
INSERT INTO public.users (telegram_id, username, name, role, is_verified_investor) VALUES
('987654321', 'test_user', 'Test User', 'investor', false);
