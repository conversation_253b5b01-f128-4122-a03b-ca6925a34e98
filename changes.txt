Of course. The provided n8n workflow has several logical issues and incomplete features that prevent it from working correctly. The primary problems are a mix of static and dynamic content paths, causing inconsistent user experiences, and the complete absence of implementation for the payment processing steps.

Here’s a breakdown of how to fix the workflow and integrate Telegram Payments.

Part 1: Fixing the Core Workflow Logic
Your workflow is trying to use both hardcoded (static) and database-driven (dynamic) content simultaneously. This creates conflicts. The fix is to make the workflow fully dynamic.

🐛 Key Issues Identified:
Conflicting Logic: The /vendors command correctly uses a dynamic list from Supabase, but the "Back to Vendors" button sometimes points to a static, hardcoded list.

Inconsistent Menus: The "Browse Menu" button leads to a generic list of categories (Mains, Drinks, etc.), which doesn't account for different menus at different vendors. The dynamic flow for selecting a vendor first is more robust.

Missing Clear Cart Function: The "Clear Cart" button in your View Cart Details node has no logic connected to it.

Unused Nodes: Several Telegram nodes with static content are either disconnected or part of a conflicting flow.

✅ How to Fix:
Unify the Vendor List:

Find the Switch node.

Locate the rule with the outputKey: CallbackBackToVendors.

Disconnect its output from the Send a text message1 node.

Connect that same output to the Get Vendors Supabase node. This ensures the user always sees the dynamic vendor list.

Streamline Menu Browse:

The "Browse Menu" button in the welcome message is redundant. It's better to have users select a vendor first to see a relevant menu.

In the Switch node, find the rule with the outputKey: CallbackMenu.

Disconnect it from Send a text message2 and connect it to the Get Vendors node. This will guide users to pick a vendor first.

You can now delete the static Telegram nodes: Send a text message1, Send a text message2, Mama J's Kitchen Details, Island Bites Details, Street Grill Details, Mains Menu, Drinks Menu, Desserts Menu, and Sides Menu. Your dynamic nodes will handle all of this.

Implement Clear Cart:

In the Switch node, add a new rule:

Condition: {{$json.callback_query.data}} -> String -> equals -> clear_cart

Output Key: ClearCart

Add a new Function node named "Clear Cart Logic" and connect the ClearCart output to it. Use this code:

JavaScript

const userId = $json.callback_query.from.id;
// Reset the cart for this user
global.set(`cart_${userId}`, { items: [], total: 0 });
return [{
  json: {
    chatId: userId,
    message: "🗑️ Your cart has been emptied."
  }
}];
Add a Telegram node named "Send Cleared Cart Confirmation". Connect the "Clear Cart Logic" node to it to send the message to the user's chatId. In the "Options" section of this Telegram node, enable Edit Message to replace the previous cart message.

Part 2: Adding Telegram Payments
To enable payments, you need to send an invoice and handle two special updates from Telegram: pre_checkout_query and successful_payment.

💳 How to Implement:
Update the Prepare Checkout Function Node:
Replace the code in your existing Prepare Checkout node with the following. This code will format the cart items into the LabeledPrice array that the Telegram Invoice node requires.

JavaScript

const userId = $json.callback_query.from.id;
const userCart = global.get(`cart_${userId}`) || { items: [], total: 0 };

if (userCart.items.length === 0) {
  // Handle empty cart error
  // This part of your code is fine
}

// Create the 'prices' array for the invoice
// Telegram requires the amount in the smallest currency unit (e.g., cents)
const prices = userCart.items.map(item => ({
  label: `${item.name} x${item.quantity}`,
  amount: Math.round(item.price * item.quantity * 100)
}));

// Prepare summary text for the invoice description
let invoiceDescription = "Your order includes:\n";
userCart.items.forEach(item => {
  invoiceDescription += `• ${item.name} x${item.quantity}\n`;
});
invoiceDescription += `\nTotal: $${userCart.total.toFixed(2)}`;

// Return all necessary data
return [{
  json: {
    prices: prices,
    total: userCart.total,
    userId: userId,
    invoiceDescription: invoiceDescription,
    // The payload is a unique ID for this transaction
    payload: `checkout-${userId}-${Date.now()}`
  }
}];
Add the Send Invoice Node:

Delete the old Send Checkout Options node.

Add a new Telegram node and connect your updated Prepare Checkout node to it.

Configure it as follows:

Resource: Send Invoice

Chat ID: {{ $json.userId }}

Title: Your Food Order

Description: {{ $json.invoiceDescription }}

Payload: {{ $json.payload }}

Provider Token: YOUR_TELEGRAM_PAYMENTS_PROVIDER_TOKEN (Get this from Telegram's @BotFather).

Currency: USD (or your desired currency).

Prices (map from data):

Source for Items: {{ $json.prices }}

Label: {{ $item.label }}

Amount: {{ $item.amount }}

Handle the Pre-Checkout Query:

In the Switch node, connect the PreCheckoutQuery output to a new Telegram node.

Configure this node:

Resource: Answer Pre-Checkout Query

Pre Checkout Query ID: {{$json.pre_checkout_query.id}}

OK: true (This tells Telegram the order is valid and payment can proceed).

Handle the Successful Payment:

Connect the SuccessfulPayment output of the Switch node to a new Function node named "Finalize Order". Use this code:

JavaScript

const userId = $json.successful_payment.from.id;
// Clear the user's cart after successful payment
global.set(`cart_${userId}`, { items: [], total: 0 });

const confirmationMessage = "✅ Thank you for your payment! Your order has been confirmed and is being prepared.";

return [{
    json: {
        chatId: userId,
        message: confirmationMessage
    }
}];
Connect the "Finalize Order" node to a Telegram node to send the confirmation message to the user's chatId.

Part 3: Recommended Improvement - Database-Driven Cart
Using global.set() for the shopping cart is not reliable, especially if your workflow runs on multiple servers. A much better approach is to store the cart in your Supabase database.

💡 How to Implement:
Create a carts Table in Supabase:

user_id (type bigint, primary key) - This will be the Telegram user ID.

cart_data (type jsonb) - This will store the items and total.

Update Your Function Nodes:
Instead of global.get() and global.set(), you would use Supabase nodes to select and upsert the cart data.

To get the cart, you would use a Supabase node to select from the carts table where user_id = {{$json.callback_query.from.id}}.

To update the cart (in the "Add Item to Cart" function), you would use a Supabase upsert operation to save the new cart_data for that user_id.







provided the corrected code please



Show thinking
Of course. Here is the corrected and completed n8n workflow JSON, which includes the logic for Telegram Payments.

You can copy the JSON below and import it directly into your n8n canvas. I have also included a summary of the JavaScript code snippets for each Function node for your reference.

Corrected n8n Workflow JSON
JSON

{
  "name": "Corrected Telegram Bot Workflow",
  "nodes": [
    {
      "parameters": {
        "updates": [
          "message",
          "callback_query",
          "pre_checkout_query",
          "successful_payment"
        ],
        "additionalFields": {}
      },
      "type": "n8n-nodes-base.telegramTrigger",
      "typeVersion": 1.2,
      "position": [
        -1920,
        -192
      ],
      "id": "820c6fcf-7729-48fc-bb85-709ba07a714b",
      "name": "Telegram Trigger",
      "webhookId": "23b39a94-f325-4058-9e19-68f6cc044920",
      "credentials": {
        "telegramApi": {
          "id": "TJrFezJVWaryfEAe",
          "name": "Telegram account"
        }
      }
    },
    {
      "parameters": {
        "rules": {
          "values": [
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "leftValue": "{{$json[\"message\"][\"text\"]}}",
                    "rightValue": "/start",
                    "operator": {
                      "type": "string",
                      "operation": "equals"
                    },
                    "id": "c4795199-7c79-4b5e-b91b-d77c3e6fc53d"
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "Welcome"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "id": "62d8cb3b-43ab-4f99-80e4-6f1741b46d42",
                    "leftValue": "={{$json[\"message\"][\"text\"]}}",
                    "rightValue": "/vendors",
                    "operator": {
                      "type": "string",
                      "operation": "equals"
                    }
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "Vendors"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "id": "callback-query-condition",
                    "leftValue": "={{$json[\"callback_query\"][\"data\"]}}",
                    "rightValue": "Vendors",
                    "operator": {
                      "type": "string",
                      "operation": "equals"
                    }
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "CallbackVendors"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "id": "callback-browse-menu",
                    "leftValue": "={{$json[\"callback_query\"][\"data\"]}}",
                    "rightValue": "browse_menu",
                    "operator": {
                      "type": "string",
                      "operation": "equals"
                    }
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "CallbackMenu"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "id": "callback-back-to-start",
                    "leftValue": "={{$json[\"callback_query\"][\"data\"]}}",
                    "rightValue": "back_to_start",
                    "operator": {
                      "type": "string",
                      "operation": "equals"
                    }
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "CallbackBackToStart"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "id": "callback-back-to-vendors",
                    "leftValue": "={{$json[\"callback_query\"][\"data\"]}}",
                    "rightValue": "back_to_vendors",
                    "operator": {
                      "type": "string",
                      "operation": "equals"
                    }
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "CallbackBackToVendors"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "id": "callback-vendor-selection",
                    "leftValue": "={{$json[\"callback_query\"][\"data\"]}}",
                    "rightValue": "vendor_",
                    "operator": {
                      "type": "string",
                      "operation": "startsWith"
                    }
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "VendorSelected"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "leftValue": "={{$json.callback_query.data}}",
                    "rightValue": "add_",
                    "operator": {
                      "type": "string",
                      "operation": "startsWith"
                    },
                    "id": "add-item-condition"
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "AddItemToCart"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "leftValue": "={{$json.callback_query.data}}",
                    "rightValue": "menu_",
                    "operator": {
                      "type": "string",
                      "operation": "startsWith"
                    },
                    "id": "vendor-menu-condition"
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "VendorMenuSelected"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "leftValue": "={{$json.update_type}}",
                    "rightValue": "pre_checkout_query",
                    "operator": {
                      "type": "string",
                      "operation": "equals"
                    },
                    "id": "pre-checkout-condition"
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "PreCheckoutQuery"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "leftValue": "={{$json.update_type}}",
                    "rightValue": "successful_payment",
                    "operator": {
                      "type": "string",
                      "operation": "equals"
                    },
                    "id": "successful-payment-condition"
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "SuccessfulPayment"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "leftValue": "={{$json.callback_query.data}}",
                    "rightValue": "view_cart",
                    "operator": {
                      "type": "string",
                      "operation": "equals"
                    },
                    "id": "view-cart-condition"
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "ViewCart"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "leftValue": "={{$json.callback_query.data}}",
                    "rightValue": "checkout",
                    "operator": {
                      "type": "string",
                      "operation": "equals"
                    },
                    "id": "checkout-condition"
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "Checkout"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "leftValue": "={{$json.callback_query.data}}",
                    "rightValue": "clear_cart",
                    "operator": {
                      "type": "string",
                      "operation": "equals"
                    },
                    "id": "clear-cart-condition"
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "ClearCart"
            }
          ]
        },
        "options": {}
      },
      "type": "n8n-nodes-base.switch",
      "typeVersion": 3.2,
      "position": [
        -1680,
        -192
      ],
      "id": "eac4cbcf-ab7f-4388-b7bb-16785bd5ac20",
      "name": "Switch"
    },
    {
      "parameters": {
        "chatId": "={{ $json.message.from.id }}",
        "text": "Choose an option below to get started.",
        "replyMarkup": "inlineKeyboard",
        "inlineKeyboard": {
          "rows": [
            {
              "row": {
                "buttons": [
                  {
                    "text": "View Vendors",
                    "additionalFields": {
                      "callback_data": "Vendors"
                    }
                  }
                ]
              }
            }
          ]
        },
        "additionalFields": {}
      },
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        -1440,
        -432
      ],
      "id": "********-42ea-4782-a419-cc6a22a3cf93",
      "name": "Send Welcome Message",
      "credentials": {
        "telegramApi": {
          "id": "TJrFezJVWaryfEAe",
          "name": "Telegram account"
        }
      }
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "callback-exists-check",
              "leftValue": "={{ $json.callback_query }}",
              "rightValue": "",
              "operator": {
                "type": "object",
                "operation": "exists"
              }
            }
          ],
          "combinator": "and"
        },
        "options": {}
      },
      "id": "f7566c35-579c-494f-9dd8-0edcba755629",
      "name": "Is Callback Query?",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [
        -1920,
        -400
      ]
    },
    {
      "parameters": {
        "resource": "callback",
        "queryId": "={{ $json.callback_query.id }}",
        "additionalFields": {}
      },
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        -1680,
        -400
      ],
      "id": "82030b2f-32da-4248-819f-c4fa993588d3",
      "name": "Answer Callback Query",
      "credentials": {
        "telegramApi": {
          "id": "TJrFezJVWaryfEAe",
          "name": "Telegram account"
        }
      }
    },
    {
      "parameters": {
        "operation": "rpc",
        "function": "get_vendors",
        "additionalFields": {}
      },
      "type": "n8n-nodes-base.supabase",
      "typeVersion": 1,
      "position": [
        -1440,
        -224
      ],
      "id": "05ca003b-25e2-408b-9031-5b0d3df02962",
      "name": "Get Vendors",
      "credentials": {
        "supabaseApi": {
          "id": "JEUpDqrenfijzyI0",
          "name": "Supabase account"
        }
      }
    },
    {
      "parameters": {
        "functionCode": "let vendor_buttons = [];\nconst vendors = items[0].json.data;\n\n// Create a button for each vendor\nfor (const vendor of vendors) {\n  vendor_buttons.push({ \n    text: vendor.name, \n    callback_data: `vendor_${vendor.id}` \n  });\n}\n\n// n8n's Telegram node expects buttons to be in nested arrays (rows)\n// Let's put 2 buttons per row for a cleaner look\nconst rows = [];\nfor (let i = 0; i < vendor_buttons.length; i += 2) {\n  rows.push(vendor_buttons.slice(i, i + 2));\n}\n\n// Add a back button on its own row\nrows.push([{ text: '⬅️ Back to Start', callback_data: 'back_to_start' }]);\n\n$item.chatId = $json.message ? $json.message.from.id : $json.callback_query.from.id;\n$item.text = 'Please select a vendor from the list below:';\n$item.inline_keyboard = rows;\n\nreturn $item;"
      },
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [
        -1220,
        -224
      ],
      "id": "0e190069-02ed-458a-96ef-44a374b04429",
      "name": "Generate Vendor Buttons"
    },
    {
      "parameters": {
        "chatId": "={{$item.chatId}}",
        "text": "={{$item.text}}",
        "replyMarkup": "inlineKeyboard",
        "inlineKeyboard": {
          "rows": "={{$item.inline_keyboard}}"
        },
        "additionalFields": {
          "parse_mode": "Markdown",
          "message_id": "{{ $json.callback_query ? $json.callback_query.message.message_id : '' }}"
        },
        "options": {
          "editMessage": true
        }
      },
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        -1000,
        -224
      ],
      "id": "7c6c7393-5cf0-4227-a8b5-a4629655d9ca",
      "name": "Send Dynamic Vendor List",
      "credentials": {
        "telegramApi": {
          "id": "TJrFezJVWaryfEAe",
          "name": "Telegram account"
        }
      }
    },
    {
      "parameters": {
        "operation": "rpc",
        "function": "get_vendor_details",
        "additionalFields": {
          "payload": "={{ { p_vendor_id: $json.callback_query.data.replace('vendor_', '') } }}"
        }
      },
      "type": "n8n-nodes-base.supabase",
      "typeVersion": 1,
      "position": [
        -1440,
        -48
      ],
      "id": "c614f8f1-8423-4b86-8ede-8d65a98e35ca",
      "name": "Get Vendor Details",
      "credentials": {
        "supabaseApi": {
          "id": "JEUpDqrenfijzyI0",
          "name": "Supabase account"
        }
      }
    },
    {
      "parameters": {
        "functionCode": "const vendor = items[0].json.data[0];\n\n// Store vendor name for later use in checkout\nconst userId = $json.callback_query.from.id;\nglobal.set(`vendor_${userId}`, vendor.name);\n\nlet menuButtons = vendor.menus.map(menu => ({ \n  text: menu.name, \n  callback_data: `menu_${menu.id}` \n}));\n\nconst rows = [];\nfor (let i = 0; i < menuButtons.length; i += 2) {\n  rows.push(menuButtons.slice(i, i + 2));\n}\n\nrows.push([{ text: '⬅️ Back to Vendors', callback_data: 'back_to_vendors' }]);\n\nlet messageText = `*${vendor.name}*\\n\\n${vendor.description}\\n\\nSelect a menu category to continue:`;\n\n$item.text = messageText;\n$item.inline_keyboard = rows;\n\nreturn $item;"
      },
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [
        -1220,
        -48
      ],
      "id": "473e7e50-dd43-4b5a-a352-ea14d111f18c",
      "name": "Generate Menu Buttons"
    },
    {
      "parameters": {
        "chatId": "={{ $json.callback_query.from.id }}",
        "text": "={{$item.text}}",
        "replyMarkup": "inlineKeyboard",
        "inlineKeyboard": {
          "rows": "={{$item.inline_keyboard}}"
        },
        "additionalFields": {
          "parse_mode": "Markdown",
          "message_id": "{{$json.callback_query.message.message_id}}"
        },
        "options": {
          "editMessage": true
        }
      },
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        -1000,
        -48
      ],
      "id": "e29108c2-98d2-4976-82d5-eca245a5b1f4",
      "name": "Send Dynamic Vendor Details",
      "credentials": {
        "telegramApi": {
          "id": "TJrFezJVWaryfEAe",
          "name": "Telegram account"
        }
      }
    },
    {
      "parameters": {
        "operation": "rpc",
        "function": "get_menu_items",
        "additionalFields": {
          "payload": "={{ { p_menu_id: $json.callback_query.data.replace('menu_', '') } }}"
        }
      },
      "type": "n8n-nodes-base.supabase",
      "typeVersion": 1,
      "position": [
        -1440,
        128
      ],
      "id": "a1b1f9bf-3e19-4325-87b4-1a4895445700",
      "name": "Get Menu Items",
      "credentials": {
        "supabaseApi": {
          "id": "JEUpDqrenfijzyI0",
          "name": "Supabase account"
        }
      }
    },
    {
      "parameters": {
        "functionCode": "const items = $item.json.data;\n\nlet messageText = 'Tap an item to add it to your cart:\\n';\n\nconst itemButtons = items.map(item => {\n  return { \n    text: `🛒 ${item.name} - $${item.price.toFixed(2)}`, \n    callback_data: `add_${item.id}`\n  };\n});\n\nconst rows = [];\n// Put each item on its own row\nfor (const button of itemButtons) {\n  rows.push([button]);\n}\n\n// Get vendor details to go back to the correct menu\nconst vendorDetailsNode = $nodes(\"Get Vendor Details\");\nconst vendorId = vendorDetailsNode[0].json.data[0].id;\n\nrows.push([{ text: '⬅️ Back to Menu Categories', callback_data: `vendor_${vendorId}` }]);\n\n$item.text = messageText;\n$item.inline_keyboard = rows;\n\nreturn $item;"
      },
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [
        -1220,
        128
      ],
      "id": "2f050b50-4386-457e-90f9-1a65530e11c1",
      "name": "Format Menu Items"
    },
    {
      "parameters": {
        "chatId": "={{ $json.callback_query.from.id }}",
        "text": "={{$item.text}}",
        "replyMarkup": "inlineKeyboard",
        "inlineKeyboard": {
          "rows": "={{$item.inline_keyboard}}"
        },
        "additionalFields": {
          "parse_mode": "Markdown",
          "message_id": "{{$json.callback_query.message.message_id}}"
        },
        "options": {
          "editMessage": true
        }
      },
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        -1000,
        128
      ],
      "id": "2dbf6809-f148-4ae0-8706-a98bc43b0b5b",
      "name": "Send Dynamic Menu Items",
      "credentials": {
        "telegramApi": {
          "id": "TJrFezJVWaryfEAe",
          "name": "Telegram account"
        }
      }
    },
    {
      "parameters": {
        "functionCode": "const callbackData = $json.callback_query.data;\nconst itemId = callbackData.replace('add_', '');\n\nreturn [{ json: { itemId: itemId } }];"
      },
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [
        -1440,
        304
      ],
      "id": "b275fc5c-d171-4938-a2b8-716ec75ba6dc",
      "name": "Extract Item Info"
    },
    {
      "parameters": {
        "operation": "select",
        "table": "items",
        "select": "*",
        "filters": {
          "filters": [
            {
              "column": "id",
              "operator": "eq",
              "value": "={{$json.itemId}}"
            }
          ]
        }
      },
      "type": "n8n-nodes-base.supabase",
      "typeVersion": 1,
      "position": [
        -1220,
        304
      ],
      "id": "b15a42e2-c51c-4d36-b49b-a980e05a5346",
      "name": "Get Item Details"
    },
    {
      "parameters": {
        "functionCode": "const item = items[0].json.data[0];\nconst userId = $json.callback_query.from.id;\n\nif (!item) {\n  return [{ json: { error: 'Item not found' } }];\n}\n\nlet userCart = global.get(`cart_${userId}`) || { items: [], total: 0 };\nlet existingItem = userCart.items.find(cartItem => cartItem.id === item.id);\n\nif (existingItem) {\n  existingItem.quantity += 1;\n} else {\n  userCart.items.push({\n    id: item.id,\n    name: item.name,\n    price: item.price,\n    quantity: 1\n  });\n}\n\nuserCart.total = userCart.items.reduce((sum, cartItem) => sum + (cartItem.price * cartItem.quantity), 0);\nglobal.set(`cart_${userId}`, userCart);\n\nreturn [{ json: { \n  message: `✅ *${item.name}* added to cart!`,\n  userId: userId\n} }];"
      },
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [
        -1000,
        304
      ],
      "id": "f4b3f6af-1567-4587-816c-af28c2314ff0",
      "name": "Add Item to Cart"
    },
    {
      "parameters": {
        "chatId": "={{$json.userId}}",
        "text": "={{$json.message}}",
        "options": {
          "showAlert": true
        }
      },
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        -780,
        304
      ],
      "id": "a578478d-591f-4cd1-8cbd-b89eb0d4e41b",
      "name": "Send 'Added to Cart' Alert",
      "notes": "This node sends a small, temporary pop-up alert to the user in Telegram to confirm an item was added, without changing the menu screen.",
      "credentials": {
        "telegramApi": {
          "id": "TJrFezJVWaryfEAe",
          "name": "Telegram account"
        }
      }
    },
    {
      "parameters": {
        "functionCode": "const userId = $json.callback_query.from.id;\nconst userCart = global.get(`cart_${userId}`) || { items: [], total: 0 };\n\nif (userCart.items.length === 0) {\n  $item.text = '🛒 Your cart is empty!';\n  $item.inline_keyboard = [[{ text: '⬅️ Back to Vendors', callback_data: 'back_to_vendors' }]];\n  return $item;\n}\n\nlet cartText = '🛒 *Your Shopping Cart*\\n\\n';\nuserCart.items.forEach(item => {\n  cartText += `*${item.name}* (x${item.quantity}) - $${(item.price * item.quantity).toFixed(2)}\\n`;\n});\ncartText += `\\n*Total: $${userCart.total.toFixed(2)}*`;\n\n$item.text = cartText;\n$item.inline_keyboard = [\n  [{ text: '💳 Proceed to Checkout', callback_data: 'checkout' }],\n  [{ text: '➕ Add More Items', callback_data: 'back_to_vendors' }, { text: '🗑️ Clear Cart', callback_data: 'clear_cart' }]\n];\n\nreturn $item;"
      },
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [
        -1440,
        480
      ],
      "id": "3626b0da-25aa-4f59-ad97-2035343caf6a",
      "name": "View Cart Details"
    },
    {
      "parameters": {
        "chatId": "={{ $json.callback_query.from.id }}",
        "text": "={{$item.text}}",
        "replyMarkup": "inlineKeyboard",
        "inlineKeyboard": {
          "rows": "={{$item.inline_keyboard}}"
        },
        "additionalFields": {
          "parse_mode": "Markdown",
          "message_id": "{{$json.callback_query.message.message_id}}"
        },
        "options": {
          "editMessage": true
        }
      },
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        -1220,
        480
      ],
      "id": "62c9a3dc-833a-4e98-b7c6-1ea88dc8d97e",
      "name": "Send Cart Details",
      "credentials": {
        "telegramApi": {
          "id": "TJrFezJVWaryfEAe",
          "name": "Telegram account"
        }
      }
    },
    {
      "parameters": {
        "functionCode": "const userId = $json.callback_query.from.id;\nconst userCart = global.get(`cart_${userId}`) || { items: [], total: 0 };\n\nif (userCart.items.length === 0) {\n  return []; // Stop execution if cart is empty\n}\n\n// Create the 'prices' array for the invoice\n// Telegram requires the amount in the smallest currency unit (e.g., cents)\nconst prices = userCart.items.map(item => ({\n  label: `${item.name} x${item.quantity}`,\n  amount: Math.round(item.price * item.quantity * 100)\n}));\n\n// Prepare summary text for the invoice description\nlet invoiceDescription = \"Your order includes:\\n\";\nuserCart.items.forEach(item => {\n  invoiceDescription += `• ${item.name} x${item.quantity}\\n`;\n});\ninvoiceDescription += `\\nTotal: $${userCart.total.toFixed(2)}`;\n\nreturn [{\n  json: {\n    prices: prices,\n    userId: userId,\n    invoiceDescription: invoiceDescription,\n    payload: `checkout-${userId}-${Date.now()}`\n  }\n}];"
      },
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [
        -1440,
        640
      ],
      "id": "08000422-ea7f-4e6e-9207-0fb649a3da62",
      "name": "Prepare Checkout"
    },
    {
      "parameters": {
        "resource": "invoice",
        "chatId": "={{$json.userId}}",
        "title": "Your Food Order",
        "description": "={{$json.invoiceDescription}}",
        "payload": "={{$json.payload}}",
        "providerToken": "YOUR_TELEGRAM_PAYMENTS_PROVIDER_TOKEN",
        "currency": "USD",
        "prices": {
          "map": {
            "items": "={{$json.prices}}",
            "label": "={{$item.label}}",
            "amount": "={{$item.amount}}"
          }
        },
        "additionalFields": {}
      },
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        -1220,
        640
      ],
      "id": "new-send-invoice-node",
      "name": "Send Invoice",
      "notes": "IMPORTANT: Replace 'YOUR_TELEGRAM_PAYMENTS_PROVIDER_TOKEN' with your actual token from @BotFather.",
      "credentials": {
        "telegramApi": {
          "id": "TJrFezJVWaryfEAe",
          "name": "Telegram account"
        }
      }
    },
    {
      "parameters": {
        "resource": "preCheckoutQuery",
        "preCheckoutQueryId": "={{$json.pre_checkout_query.id}}",
        "ok": true,
        "additionalFields": {}
      },
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        -1440,
        800
      ],
      "id": "new-answer-pre-checkout-node",
      "name": "Answer Pre-Checkout Query",
      "credentials": {
        "telegramApi": {
          "id": "TJrFezJVWaryfEAe",
          "name": "Telegram account"
        }
      }
    },
    {
      "parameters": {
        "functionCode": "const userId = $json.successful_payment.from.id;\n// Clear the user's cart after successful payment\nglobal.set(`cart_${userId}`, { items: [], total: 0 });\n\nconst confirmationMessage = \"✅ Thank you for your payment! Your order has been confirmed and is being prepared.\";\n\nreturn [{\n    json: {\n        chatId: userId,\n        message: confirmationMessage\n    }\n}];"
      },
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [
        -1440,
        960
      ],
      "id": "new-finalize-order-node",
      "name": "Finalize Order"
    },
    {
      "parameters": {
        "chatId": "={{$json.chatId}}",
        "text": "={{$json.message}}",
        "additionalFields": {}
      },
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        -1220,
        960
      ],
      "id": "new-send-confirmation-node",
      "name": "Send Payment Confirmation",
      "credentials": {
        "telegramApi": {
          "id": "TJrFezJVWaryfEAe",
          "name": "Telegram account"
        }
      }
    },
    {
      "parameters": {
        "functionCode": "const userId = $json.callback_query.from.id;\n// Reset the cart for this user\nglobal.set(`cart_${userId}`, { items: [], total: 0 });\nreturn [{\n  json: {\n    chatId: userId,\n    message: \"🗑️ Your cart has been emptied.\",\n    message_id: $json.callback_query.message.message_id\n  }\n}];"
      },
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [
        -1440,
        1120
      ],
      "id": "new-clear-cart-logic-node",
      "name": "Clear Cart Logic"
    },
    {
      "parameters": {
        "chatId": "={{$json.chatId}}",
        "text": "={{$json.message}}",
        "additionalFields": {
          "message_id": "{{$json.message_id}}"
        },
        "options": {
          "editMessage": true
        }
      },
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        -1220,
        1120
      ],
      "id": "new-send-cleared-cart-confirmation-node",
      "name": "Send Cleared Cart Confirmation",
      "credentials": {
        "telegramApi": {
          "id": "TJrFezJVWaryfEAe",
          "name": "Telegram account"
        }
      }
    }
  ],
  "pinData": {},
  "connections": {
    "Telegram Trigger": {
      "main": [
        [
          {
            "node": "Switch",
            "type": "main",
            "index": 0
          },
          {
            "node": "Is Callback Query?",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Switch": {
      "main": [
        [
          {
            "node": "Send Welcome Message",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Get Vendors",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Get Vendors",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Get Vendors",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Send Welcome Message",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Get Vendors",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Get Vendor Details",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Extract Item Info",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Get Menu Items",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Answer Pre-Checkout Query",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Finalize Order",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "View Cart Details",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Prepare Checkout",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Clear Cart Logic",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Is Callback Query?": {
      "main": [
        [
          {
            "node": "Answer Callback Query",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Get Vendors": {
      "main": [
        [
          {
            "node": "Generate Vendor Buttons",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Generate Vendor Buttons": {
      "main": [
        [
          {
            "node": "Send Dynamic Vendor List",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Get Vendor Details": {
      "main": [
        [
          {
            "node": "Generate Menu Buttons",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Generate Menu Buttons": {
      "main": [
        [
          {
            "node": "Send Dynamic Vendor Details",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Get Menu Items": {
      "main": [
        [
          {
            "node": "Format Menu Items",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Format Menu Items": {
      "main": [
        [
          {
            "node": "Send Dynamic Menu Items",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Extract Item Info": {
      "main": [
        [
          {
            "node": "Get Item Details",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Get Item Details": {
      "main": [
        [
          {
            "node": "Add Item to Cart",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Add Item to Cart": {
      "main": [
        [
          {
            "node": "Send 'Added to Cart' Alert",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "View Cart Details": {
      "main": [
        [
          {
            "node": "Send Cart Details",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Prepare Checkout": {
      "main": [
        [
          {
            "node": "Send Invoice",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Finalize Order": {
      "main": [
        [
          {
            "node": "Send Payment Confirmation",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Clear Cart Logic": {
      "main": [
        [
          {
            "node": "Send Cleared Cart Confirmation",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "active": false,
  "settings": {
    "executionOrder": "v1"
  },
  "versionId": "c530441f-6d7e-44b2-911a-8813a0c1ef84",
  "meta": {
    "templateCredsSetupCompleted": true
  },
  "id": "QrP0bJgkM7BfiDj8",
  "tags": []
}
Summary of Code Snippets
Here is the JavaScript for each of the Function nodes in the corrected workflow.

Generate Vendor Buttons
JavaScript

let vendor_buttons = [];
const vendors = items[0].json.data;

// Create a button for each vendor
for (const vendor of vendors) {
  vendor_buttons.push({
    text: vendor.name,
    callback_data: `vendor_${vendor.id}`
  });
}

// n8n's Telegram node expects buttons to be in nested arrays (rows)
// Let's put 2 buttons per row for a cleaner look
const rows = [];
for (let i = 0; i < vendor_buttons.length; i += 2) {
  rows.push(vendor_buttons.slice(i, i + 2));
}

// Add a back button on its own row
rows.push([{ text: '⬅️ Back to Start', callback_data: 'back_to_start' }]);

$item.chatId = $json.message ? $json.message.from.id : $json.callback_query.from.id;
$item.text = 'Please select a vendor from the list below:';
$item.inline_keyboard = rows;

return $item;
Generate Menu Buttons
JavaScript

const vendor = items[0].json.data[0];

// Store vendor name for later use in checkout
const userId = $json.callback_query.from.id;
global.set(`vendor_${userId}`, vendor.name);

let menuButtons = vendor.menus.map(menu => ({
  text: menu.name,
  callback_data: `menu_${menu.id}`
}));

const rows = [];
for (let i = 0; i < menuButtons.length; i += 2) {
  rows.push(menuButtons.slice(i, i + 2));
}

rows.push([{ text: '⬅️ Back to Vendors', callback_data: 'back_to_vendors' }]);

let messageText = `*${vendor.name}*\n\n${vendor.description}\n\nSelect a menu category to continue:`;

$item.text = messageText;
$item.inline_keyboard = rows;

return $item;
Format Menu Items
JavaScript

const items = $item.json.data;

let messageText = 'Tap an item to add it to your cart:\n';

const itemButtons = items.map(item => {
  return {
    text: `🛒 ${item.name} - $${item.price.toFixed(2)}`,
    callback_data: `add_${item.id}`
  };
});

const rows = [];
// Put each item on its own row
for (const button of itemButtons) {
  rows.push([button]);
}

// Get vendor details to go back to the correct menu
const vendorDetailsNode = $nodes("Get Vendor Details");
const vendorId = vendorDetailsNode[0].json.data[0].id;

rows.push([{ text: '⬅️ Back to Menu Categories', callback_data: `vendor_${vendorId}` }]);

$item.text = messageText;
$item.inline_keyboard = rows;

return $item;
Add Item to Cart
JavaScript

const item = items[0].json.data[0];
const userId = $json.callback_query.from.id;

if (!item) {
  return [{ json: { error: 'Item not found' } }];
}

let userCart = global.get(`cart_${userId}`) || { items: [], total: 0 };
let existingItem = userCart.items.find(cartItem => cartItem.id === item.id);

if (existingItem) {
  existingItem.quantity += 1;
} else {
  userCart.items.push({
    id: item.id,
    name: item.name,
    price: item.price,
    quantity: 1
  });
}

userCart.total = userCart.items.reduce((sum, cartItem) => sum + (cartItem.price * cartItem.quantity), 0);
global.set(`cart_${userId}`, userCart);

return [{ json: {
  message: `✅ *${item.name}* added to cart!`,
  userId: userId
} }];
View Cart Details
JavaScript

const userId = $json.callback_query.from.id;
const userCart = global.get(`cart_${userId}`) || { items: [], total: 0 };

if (userCart.items.length === 0) {
  $item.text = '🛒 Your cart is empty!';
  $item.inline_keyboard = [[{ text: '⬅️ Back to Vendors', callback_data: 'back_to_vendors' }]];
  return $item;
}

let cartText = '🛒 *Your Shopping Cart*\n\n';
userCart.items.forEach(item => {
  cartText += `*${item.name}* (x${item.quantity}) - $${(item.price * item.quantity).toFixed(2)}\n`;
});
cartText += `\n*Total: $${userCart.total.toFixed(2)}*`;

$item.text = cartText;
$item.inline_keyboard = [
  [{ text: '💳 Proceed to Checkout', callback_data: 'checkout' }],
  [{ text: '➕ Add More Items', callback_data: 'back_to_vendors' }, { text: '🗑️ Clear Cart', callback_data: 'clear_cart' }]
];

return $item;
Clear Cart Logic
JavaScript

const userId = $json.callback_query.from.id;
// Reset the cart for this user
global.set(`cart_${userId}`, { items: [], total: 0 });
return [{
  json: {
    chatId: userId,
    message: "🗑️ Your cart has been emptied.",
    message_id: $json.callback_query.message.message_id
  }
}];
Prepare Checkout
JavaScript

const userId = $json.callback_query.from.id;
const userCart = global.get(`cart_${userId}`) || { items: [], total: 0 };

if (userCart.items.length === 0) {
  return []; // Stop execution if cart is empty
}

// Create the 'prices' array for the invoice
// Telegram requires the amount in the smallest currency unit (e.g., cents)
const prices = userCart.items.map(item => ({
  label: `${item.name} x${item.quantity}`,
  amount: Math.round(item.price * item.quantity * 100)
}));

// Prepare summary text for the invoice description
let invoiceDescription = "Your order includes:\n";
userCart.items.forEach(item => {
  invoiceDescription += `• ${item.name} x${item.quantity}\n`;
});
invoiceDescription += `\nTotal: $${userCart.total.toFixed(2)}`;

return [{
  json: {
    prices: prices,
    userId: userId,
    invoiceDescription: invoiceDescription,
    payload: `checkout-${userId}-${Date.now()}`
  }
}];
Finalize Order
JavaScript

const userId = $json.successful_payment.from.id;
// Clear the user's cart after successful payment
global.set(`cart_${userId}`, { items: [], total: 0 });

const confirmationMessage = "✅ Thank you for your payment! Your order has been confirmed and is being prepared.";

return [{
    json: {
        chatId: userId,
        message: confirmationMessage
    }
}];