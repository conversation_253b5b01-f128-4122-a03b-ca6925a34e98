# ELOH Processing Bot - Enhanced Workflow with Schema Integration

This enhanced n8n workflow integrates with a comprehensive database schema to provide a full-featured Telegram bot for the ELOH Processing DAO.

## 🚀 Features

### Core Functionality
- **User Management**: Automatic user registration and profile management
- **Dynamic Menus**: Database-driven menu system with role-based access
- **Vendor System**: Multi-vendor marketplace for services
- **Shopping Cart**: Full e-commerce cart functionality
- **Order Management**: Complete order lifecycle tracking
- **Payment Processing**: Integrated payment handling with Telegram Payments
- **Admin Panel**: Administrative functions for bot management

### Enhanced Capabilities
- **Role-Based Access**: Different interfaces for admins, investors, and regular users
- **Service Marketplace**: Browse and purchase various ELOH services
- **Investment Tracking**: Monitor investment status and portfolio
- **Donation System**: Support the DAO with various donation options
- **Analytics**: Track user engagement and order metrics

## 📁 Files Included

1. **enhanced-eloh-workflow.json** - Main n8n workflow file
2. **enhanced-schema.sql** - Complete database schema
3. **sample-data.sql** - Sample data for testing
4. **eloh-processing-bot-workflow.json** - Fixed original workflow (backup)

## 🛠️ Setup Instructions

### 1. Database Setup

```sql
-- Run the enhanced schema
\i enhanced-schema.sql

-- Load sample data (optional, for testing)
\i sample-data.sql
```

### 2. n8n Workflow Import

1. Open your n8n instance
2. Go to Workflows → Import from File
3. Select `enhanced-eloh-workflow.json`
4. Configure credentials:
   - **Telegram API**: Your bot token and credentials
   - **Supabase API**: Your database connection details

### 3. Credential Configuration

Update the following credential IDs in the workflow:
- `S3mx1dNYh7twonoL` - Replace with your Telegram API credential ID
- `JEUpDqrenfijzyI0` - Replace with your Supabase API credential ID

### 4. Bot Configuration

Update the bot username in the "Set Variables" node:
```json
{
  "botUsername": "your_bot_username"
}
```

## 🗃️ Database Schema Overview

### Core Tables

#### Users (`public.users`)
- Stores user profiles with Telegram integration
- Supports role-based access (admin, investor, vendor)
- Tracks verification status and investment details

#### Vendors (`public.vendors`)
- Service providers in the marketplace
- Can be linked to user accounts
- Supports contact information and branding

#### Menus & Menu Items (`public.menus`, `public.menu_items`)
- Hierarchical service catalog
- Supports pricing, availability, and metadata
- Enables dynamic service offerings

#### Orders & Carts (`public.orders`, `public.carts`)
- Complete e-commerce functionality
- Order lifecycle tracking
- Shopping cart persistence

#### Bot Configuration (`public.bot_menu`, `public.menu_responses`)
- Dynamic menu system
- Configurable bot responses
- Role-based menu visibility

### Key Features

#### Dynamic Menu System
The bot menu is driven by the `bot_menu` table, allowing for:
- Runtime menu configuration
- Role-based button visibility
- A/B testing of menu layouts

#### Service Marketplace
Multi-vendor support with:
- Categorized services
- Flexible pricing models
- Service availability management

#### Order Management
Complete order lifecycle:
- Cart → Order → Payment → Fulfillment
- Status tracking and notifications
- Payment integration with Telegram

## 🔧 Workflow Components

### Main Flow
1. **Telegram Trigger** - Receives all bot interactions
2. **User Data Standardization** - Normalizes user information
3. **Event Router** - Routes different types of events
4. **User Management** - Handles user registration/lookup
5. **Main Switch** - Routes to appropriate handlers

### Enhanced Features
1. **Vendor Browser** - Lists available service providers
2. **Cart Management** - Shopping cart operations
3. **Order History** - User order tracking
4. **Admin Functions** - Administrative operations

### Database Integration
- All operations use Supabase for data persistence
- Proper error handling and data validation
- Optimized queries with appropriate indexes

## 🎯 Usage Examples

### For Regular Users
- Browse services from different vendors
- Add items to cart and checkout
- View order history and status
- Make donations to support the DAO

### For Verified Investors
- Access investment portfolio information
- View detailed roadmap and progress
- Priority access to new services

### For Admins
- Manage users and their roles
- Monitor order and payment status
- Configure bot menus and responses
- Access analytics and reporting

## 🔒 Security Features

- Role-based access control
- Input validation and sanitization
- Secure payment processing
- Audit trails for all transactions

## 📊 Analytics & Monitoring

The schema includes fields for tracking:
- User engagement metrics
- Order conversion rates
- Service popularity
- Revenue analytics

## 🚀 Deployment

1. Set up your Supabase database
2. Run the schema and sample data scripts
3. Import the workflow into n8n
4. Configure credentials and bot settings
5. Test with sample data
6. Deploy to production

## 🔄 Maintenance

Regular maintenance tasks:
- Monitor database performance
- Update service offerings
- Review user feedback
- Optimize workflow performance

## 📞 Support

For issues or questions:
- Check the workflow logs in n8n
- Review database constraints and indexes
- Verify credential configurations
- Test with sample data first

This enhanced system provides a robust foundation for the ELOH Processing DAO Telegram bot with full e-commerce capabilities and administrative features.
