{"name": "ELOH Bot V2 - Integrated Telegram Payments", "nodes": [{"parameters": {"updates": ["message", "callback_query", "pre_checkout_query", "successful_payment"], "additionalFields": {}}, "id": "6f89e99a-36d9-44b7-bda8-7c335c576acf", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [-1660, -140], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{ $json.pre_checkout_query }}", "operation": {"type": "exists"}}, {"leftValue": "={{ $json.message.successful_payment || $json.successful_payment }}", "operation": {"type": "exists"}, "output": 2}]}, "options": {}}, "id": "NEW-Event-Router", "name": "Event Router", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-1460, -140], "notes": "Routes incoming events. User actions go to the main logic, while payment events have dedicated handlers."}, {"parameters": {"values": {"string": [{"name": "botUsername", "value": "elohprocessingbot"}]}, "options": {}}, "id": "c4966a0f-c6c7-4eff-9825-febc5c6771c8", "name": "Set Variables", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [-1280, -140]}, {"parameters": {"dataType": "string", "value1": "={{ $json.message ? ($json.message.text.includes(' ') ? $json.message.text.split(' ')[0] : $json.message.text) : ($json.callback_query ? $json.callback_query.data : ($json.chat_member ? 'new_member' : '')) }}", "rules": {"rules": [{"value2": "/start", "output": 0}, {"value2": "main_menu", "output": 0}, {"value2": "show_roadmap_public", "output": 1}, {"value2": "view_investment", "output": 2}, {"value2": "new_member", "output": 3}, {"value2": "services_menu", "output": 4}, {"value2": "support_invest_menu", "output": 5}, {"value2": "invest_1000", "output": 6}, {"value2": "donate_100", "output": 7}]}}, "id": "740926b8-52dd-4232-ba7f-e4a67990593f", "name": "Main Switch", "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [-1100, -140]}, {"parameters": {"operation": "get", "tableId": "project_roadmap", "filters": {"conditions": [{}]}}, "id": "c777d33a-0163-4527-a2a1-7cf92c31a980", "name": "Get Roadmap Data", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-840, -192], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"jsCode": "const items = $input.all();\nlet formattedText = '🗺️ **ELOH Processing Public Roadmap**\\n\\n';\nfor (const item of items) {\n  formattedText += `- **${item.json.name}**: ${item.json.status}\\n`;\n}\n\n$input.first().json.formatted_text = formattedText;\nreturn $input.first();"}, "id": "82aa6a7d-1d10-4d7f-bfac-99ac26f7e7c3", "name": "Format Roadmap Text", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-640, -192]}, {"parameters": {"chatId": "={{ $json.callback_query.message.chat.id }}", "text": "={{ $json.formatted_text }}", "additionalFields": {}}, "id": "73cb6ff2-ccf4-4f7d-a87d-00b85cbc1aa1", "name": "Send Roadmap", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-440, -192], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.message ? $json.message.chat.id : $json.callback_query.message.chat.id }}", "text": "🚀 **Welcome to ELOH Processing DAO**\n\nUse the buttons below to explore our project.", "parseMode": "MarkdownV2", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "🌟 Support & Invest", "additionalFields": {"callback_data": "support_invest_menu"}}, {"text": "🗺️ Public Roadmap", "additionalFields": {"callback_data": "show_roadmap_public"}}]}}, {"row": {"buttons": [{"text": "🔧 Our Services", "additionalFields": {"callback_data": "services_menu"}}, {"text": "💰 My Investor Portal", "additionalFields": {"callback_data": "view_investment"}}]}}]}}, "id": "db4f1791-ca21-4a54-bf3e-3d8a0c6c5ca2", "name": "Send Main Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-840, -12], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL"}}}, {"parameters": {"jsCode": "const menu = {\n  text: `🌟 **Support Our Mission & Invest**\\n\\nJoin our mission by making a direct contribution through Telegram's secure payment system.\\n\\n**Tiers:**\\n- **VIP Member** ($100): Access to our private community & monthly reports.\\n- **Verified Investor** ($1,000): All VIP perks plus profit sharing and governance rights.`,\n  reply_markup: {\n    inline_keyboard: [\n      [\n        {text: \"💎 Become an Investor ($1,000)\", callback_data: \"invest_1000\"}\n      ],\n      [\n        {text: \"✨ Become a VIP ($100)\", callback_data: \"donate_100\"}\n      ],\n      [\n        {text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\"}\n      ]\n    ]\n  }\n};\n\n$input.first().json.menuData = menu;\nreturn $input.first();"}, "id": "NEW-Build-Support-Menu", "name": "Build Support & Invest Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-840, 788]}, {"parameters": {"chatId": "={{ $json.callback_query.message.chat.id }}", "text": "={{ $json.menuData.text }}", "parseMode": "MarkdownV2", "replyMarkup": "={{ $json.menuData.reply_markup }}", "additionalFields": {}}, "id": "NEW-Send-Support-Menu", "name": "Send Support & Invest Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-640, 788], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL"}}}, {"parameters": {"chatId": "={{ $json.callback_query.message.chat.id }}", "title": "Verified Investor Package", "description": "One-time investment to gain profit-sharing and governance rights in the ELOH Processing DAO.", "payload": "investor-{{$json.callback_query.from.id}}", "providerToken": "={{ $credentials.telegramPaymentProvider.token }}", "currency": "USD", "prices": {"prices": [{"label": "Investment", "amount": 100000}]}, "additionalFields": {}}, "id": "NEW-Send-Investor-Invoice", "name": "Send Investor Invoice", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-840, 968], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL"}, "telegramPaymentProvider": {"id": "YOUR_PROVIDER_ID"}}}, {"parameters": {"chatId": "={{ $json.callback_query.message.chat.id }}", "title": "VIP Membership", "description": "Become a VIP member to gain access to our private community and receive monthly progress reports.", "payload": "vip-{{$json.callback_query.from.id}}", "providerToken": "={{ $credentials.telegramPaymentProvider.token }}", "currency": "USD", "prices": {"prices": [{"label": "Donation", "amount": 10000}]}, "additionalFields": {}}, "id": "NEW-Send-VIP-Invoice", "name": "Send VIP Invoice", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-840, 1148], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL"}, "telegramPaymentProvider": {"id": "YOUR_PROVIDER_ID"}}}, {"parameters": {"preCheckoutQueryId": "={{ $json.pre_checkout_query.id }}", "ok": true, "additionalFields": {}}, "id": "NEW-Pre-Checkout-Answer", "name": "Answer Pre-Checkout Query", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1220, 120], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL"}}, "notes": "Confirms to Telegram that the bot is ready to accept the payment."}, {"parameters": {"operation": "upsert", "tableId": "users", "matchColumns": "telegram_id", "columns": {"columns": [{"keyName": "telegram_id", "value": "={{ $json.message.successful_payment.from.id }}"}, {"keyName": "is_verified_investor", "value": "={{ $json.message.successful_payment.invoice_payload.startsWith('investor') }}"}, {"keyName": "is_vip", "value": "={{ $json.message.successful_payment.invoice_payload.startsWith('vip') }}"}, {"keyName": "last_payment_amount", "value": "={{ $json.message.successful_payment.total_amount / 100 }}"}, {"keyName": "name", "value": "={{ $json.message.successful_payment.from.first_name }}"}]}}, "id": "NEW-Upsert-User", "name": "Upsert User After Payment", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-1220, 420], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0"}}}, {"parameters": {"chatId": "={{ $json.message.successful_payment.from.id }}", "text": "✅ **Payment Successful!**\n\nCongratulations and thank you for your support! Your status has been upgraded.\n\nYou can now access exclusive features via the main menu.", "parseMode": "MarkdownV2", "additionalFields": {}}, "id": "NEW-Send-Confirmation", "name": "Send Payment Confirmation", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1020, 420], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL"}}}], "pinData": {}, "connections": {"Telegram Trigger": {"main": [[{"node": "Event Router", "type": "main", "index": 0}]]}, "Event Router": {"main": [[{"node": "Set Variables", "type": "main", "index": 0}], [{"node": "Answer Pre-Checkout Query", "type": "main", "index": 0}], [{"node": "Upsert User After Payment", "type": "main", "index": 0}]]}, "Set Variables": {"main": [[{"node": "Main Switch", "type": "main", "index": 0}]]}, "Main Switch": {"main": [[{"node": "Send Main Menu", "type": "main", "index": 0}], [{"node": "Get Roadmap Data", "type": "main", "index": 0}], [{"node": "Build Support & Invest Menu", "type": "main", "index": 5}], [{"node": "Send Investor Invoice", "type": "main", "index": 0}], [{"node": "Send VIP Invoice", "type": "main", "index": 0}]]}, "Get Roadmap Data": {"main": [[{"node": "Format Roadmap Text", "type": "main", "index": 0}]]}, "Format Roadmap Text": {"main": [[{"node": "Send Roadmap", "type": "main", "index": 0}]]}, "Build Support & Invest Menu": {"main": [[{"node": "Send Support & Invest Menu", "type": "main", "index": 0}]]}, "Upsert User After Payment": {"main": [[{"node": "Send Payment Confirmation", "type": "main", "index": 0}]]}}, "active": true, "settings": {}, "id": "v2_integrated_payments"}