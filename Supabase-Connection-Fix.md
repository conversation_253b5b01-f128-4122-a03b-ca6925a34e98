# Supabase Connection Fix - ELOH Processing Bot

## 🔍 Issue Identified
**Problem**: Database data is not being queried into the bot from your Supabase account
**Root Cause**: Inconsistent Supabase node configurations and query formats

## 🛠️ Fixes Applied (Preserving Your Credentials)

### **Your Existing Credentials Preserved**
- ✅ Supabase Credential ID: `JEUpDqrenfijzyI0`
- ✅ Telegram Credential ID: `S3mx1dNYh7twonoL`
- ✅ All credential references maintained

### **1. Fixed "Check Existing User" Node**

#### Before (Problematic):
```json
{
  "operation": "executeQuery",
  "query": "SELECT * FROM users WHERE telegram_id = '{{ $json.unifiedUserId }}' LIMIT 1"
}
```

#### After (Fixed):
```json
{
  "operation": "get",
  "tableId": "users",
  "filters": {
    "conditions": [
      {
        "keyName": "telegram_id",
        "value": "={{ $json.unifiedUserId }}"
      }
    ]
  }
}
```

### **2. Fixed "Create New User" Node**

#### Before (Problematic):
```json
{
  "operation": "executeQuery",
  "query": "INSERT INTO users (...) VALUES (...) RETURNING *"
}
```

#### After (Fixed):
```json
{
  "operation": "insert",
  "tableId": "users",
  "columns": {
    "columns": [
      {"keyName": "telegram_id", "value": "={{ $json.unifiedUserId }}"},
      {"keyName": "username", "value": "={{ $json.unifiedUsername || '' }}"},
      {"keyName": "name", "value": "={{ name_expression }}"},
      {"keyName": "is_verified_investor", "value": false},
      {"keyName": "role", "value": "investor"}
    ]
  }
}
```

### **3. Added Comprehensive Debug Nodes**

#### Test Supabase Connection:
```javascript
console.log('=== ABOUT TO QUERY SUPABASE ===');
console.log('User ID to search:', $json.unifiedUserId);
console.log('User ID type:', typeof $json.unifiedUserId);
console.log('Full data object:', JSON.stringify($json, null, 2));
```

#### Enhanced Supabase Result Debug:
```javascript
console.log('=== SUPABASE QUERY RESULT ===');
console.log('Raw response:', JSON.stringify($json, null, 2));
console.log('Response type:', typeof $json);
console.log('Is array:', Array.isArray($json));
if (Array.isArray($json)) {
  console.log('Array length:', $json.length);
  console.log('First item:', $json[0]);
}
```

## 🔧 Testing Your Supabase Connection

### **1. Check n8n Execution Logs**
After importing the fixed workflow, test with `/start` and look for:

```
=== ABOUT TO QUERY SUPABASE ===
User ID to search: 123456789
User ID type: string
Full data object: { unifiedUserId: "123456789", ... }

=== SUPABASE QUERY RESULT ===
Raw response: []  // Empty array for new user
Response type: object
Is array: true
Array length: 0
```

### **2. Verify Supabase Directly**
Test your connection in Supabase SQL Editor:
```sql
-- Test if table exists and has data
SELECT * FROM users LIMIT 5;

-- Test the specific query the bot uses
SELECT * FROM users WHERE telegram_id = '123456789';

-- Check table structure
\d users
```

### **3. Check Your Supabase Credentials in n8n**
Verify your credential `JEUpDqrenfijzyI0` has:
- ✅ Correct Supabase URL (https://your-project.supabase.co)
- ✅ Valid API Key (service_role key for full access)
- ✅ Proper permissions

## 🚨 Common Supabase Issues & Solutions

### **Issue 1: "Table 'users' does not exist"**
**Solution**: Run the enhanced schema in your Supabase SQL Editor
```sql
-- Copy and paste from enhanced-schema.sql
CREATE TABLE public.users (
  user_id uuid NOT NULL DEFAULT uuid_generate_v4(),
  telegram_id text UNIQUE NOT NULL,
  -- ... rest of schema
);
```

### **Issue 2: "Permission denied for table users"**
**Solution**: Check Row Level Security (RLS) policies
```sql
-- Disable RLS temporarily for testing
ALTER TABLE users DISABLE ROW LEVEL SECURITY;

-- Or add proper policy for service role
CREATE POLICY "Service role can access users" 
ON users FOR ALL TO service_role;
```

### **Issue 3: "Invalid API key"**
**Solution**: Use the service_role key, not anon key
- Go to Supabase Dashboard → Settings → API
- Copy the `service_role` key (not `anon` key)
- Update your n8n credential

### **Issue 4: "Connection timeout"**
**Solution**: Check your Supabase URL format
```
✅ Correct: https://your-project-id.supabase.co
❌ Wrong: https://your-project-id.supabase.co/rest/v1
```

## 📊 Expected Debug Output

### **Successful Connection (New User)**:
```
=== ABOUT TO QUERY SUPABASE ===
User ID to search: 123456789
=== SUPABASE QUERY RESULT ===
Raw response: []
Array length: 0
New user created: { user_id: "uuid", telegram_id: "123456789" }
```

### **Successful Connection (Existing User)**:
```
=== ABOUT TO QUERY SUPABASE ===
User ID to search: 123456789
=== SUPABASE QUERY RESULT ===
Raw response: [{ user_id: "uuid", telegram_id: "123456789", ... }]
Array length: 1
Existing user found: { user_id: "uuid", ... }
```

### **Connection Error**:
```
Error: connect ECONNREFUSED
// OR
Error: Invalid API key
// OR
Error: relation "users" does not exist
```

## ✅ Verification Steps

### **Before Testing**:
- [ ] Import updated `enhanced-eloh-workflow.json`
- [ ] Verify credential `JEUpDqrenfijzyI0` is configured
- [ ] Check Supabase table `users` exists
- [ ] Confirm RLS policies allow access

### **During Testing**:
- [ ] Send `/start` from Telegram
- [ ] Check n8n execution logs for debug output
- [ ] Verify Supabase queries in the logs
- [ ] Confirm data flows through all nodes

### **After Testing**:
- [ ] New users appear in Supabase `users` table
- [ ] Existing users are recognized correctly
- [ ] Bot responds with appropriate messages
- [ ] No connection errors in logs

## 🎯 Next Steps

1. **Import Fixed Workflow**: Your credentials are preserved
2. **Test Connection**: Monitor n8n logs for Supabase queries
3. **Verify Database**: Check that queries reach your Supabase
4. **Remove Debug**: Once working, remove debug nodes for production

The Supabase connection should now work correctly with your existing credentials!
