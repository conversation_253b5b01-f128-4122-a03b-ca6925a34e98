{"name": "My workflow copy", "nodes": [{"parameters": {"updates": ["message", "chat_member", "callback_query", "pre_checkout_query"], "additionalFields": {}}, "id": "da252984-2b03-4030-87d4-e6746386f9d9", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [-2736, -80], "webhookId": "76bfb192-83ad-4541-9197-36d47cf7049b", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"values": {"string": [{"name": "botUsername", "value": "elohprocessingbot"}]}, "options": {}}, "id": "a15e4765-7cd8-4a37-80a7-470232baccb5", "name": "Set Variables", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [-2592, -80]}, {"parameters": {"jsCode": "const message = $json.message;\nconst callbackQuery = $json.callback_query;\nconst chatMember = $json.chat_member;\n\nlet userId = (message && message.from && message.from.id) || (callbackQuery && callbackQuery.from && callbackQuery.from.id) || (chatMember && chatMember.new_chat_member && chatMember.new_chat_member.user && chatMember.new_chat_member.user.id);\n\nlet chatId = (message && message.chat && message.chat.id) || (callbackQuery && callbackQuery.message && callbackQuery.message.chat && callbackQuery.message.chat.id) || (chatMember && chatMember.chat && chatMember.chat.id);\n\nlet username = (message && message.from && message.from.username) || (callbackQuery && callbackQuery.from && callbackQuery.from.username) || (chatMember && chatMember.new_chat_member && chatMember.new_chat_member.user && chatMember.new_chat_member.user.username);\n\nlet firstName = (message && message.from && message.from.first_name) || (callbackQuery && callbackQuery.from && callbackQuery.from.first_name) || (chatMember && chatMember.new_chat_member && chatMember.new_chat_member.user && chatMember.new_chat_member.user.first_name);\n\nlet lastName = (message && message.from && message.from.last_name) || (callbackQuery && callbackQuery.from && callbackQuery.from.last_name) || (chatMember && chatMember.new_chat_member && chatMember.new_chat_member.user && chatMember.new_chat_member.user.last_name);\n\nlet isCallback = !!callbackQuery;\nlet isNewChatMember = !!chatMember;\n\nreturn {\n  json: {\n    ...$json,\n    unifiedUserId: userId,\n    unifiedChatId: chatId,\n    unifiedUsername: username,\n    unifiedFirstName: firstName,\n    unifiedLastName: lastName,\n    isCallback: isCallback,\n    isNewChatMember: isNewChatMember\n  }\n};"}, "id": "ad13371d-62d3-4075-b6cd-26eefa623770", "name": "Standardize User Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2304, -80]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.pre_checkout_query }}", "rightValue": "", "operation": {"type": "string", "operation": "isNotEmpty", "rightType": "any"}}, {"leftValue": "={{ $json.message?.successful_payment }}", "rightValue": "", "operation": {"type": "string", "operation": "isNotEmpty", "rightType": "any"}}], "combineOperation": "any"}, "options": {}}, "id": "7ee7f8db-d868-4fb7-a63c-fffea66fbe62", "name": "Event Router", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-2160, -80]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.unifiedUserId }}", "rightValue": "", "operation": {"type": "string", "operation": "isNotEmpty", "rightType": "any"}}, {"leftValue": "={{ typeof $json.unifiedUserId }}", "rightValue": "undefined", "operation": {"type": "string", "operation": "notEqual", "rightType": "any"}}], "combineOperation": "all"}, "options": {}}, "id": "9c16f629-9ef1-4d83-8a7f-ba5bfedd6a48", "name": "Validate User ID", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-2656, 112]}, {"parameters": {"jsCode": "// Store original user data for later use\nconst originalData = $json;\nconsole.log('Storing original user data:', JSON.stringify(originalData, null, 2));\nconsole.log('Unified User ID:', originalData.unifiedUserId);\nconsole.log('Type of User ID:', typeof originalData.unifiedUserId);\n\n// Pass through the original data\nreturn { json: originalData };"}, "id": "6e805c88-1c61-416b-b09d-5b6e093866c2", "name": "Debug Before User Lookup", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2448, 96]}, {"parameters": {"jsCode": "// Test Supabase connection by checking if we can reach this point\nconsole.log('=== ABOUT TO QUERY SUPABASE ===');\nconsole.log('User ID to search:', $json.unifiedUserId);\nconsole.log('User ID type:', typeof $json.unifiedUserId);\nconsole.log('Full data object:', JSON.stringify($json, null, 2));\nreturn $input.all();"}, "id": "fd094261-15a2-4565-a21e-64a92dbeb601", "name": "Test Supabase Connection", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2448, -80]}, {"parameters": {"operation": "getAll", "tableId": "users", "returnAll": true, "filters": {"conditions": [{"keyName": "telegram_id", "condition": "eq", "keyValue": "={{ $json.unifiedUserId.toString() }}"}, {"keyName": "user_id", "condition": "is", "keyValue": "null"}]}}, "id": "92fb45a8-f28c-433d-bb19-6e95f9a324b2", "name": "Check Existing User", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-2304, 96], "alwaysOutputData": true, "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"jsCode": "// Merge Supabase results with original user data\nconst originalData = $('Debug Before User Lookup').item.json;\nconst supabaseResults = $json; // Current input is from Supabase\n\nconsole.log('=== MERGING USER DATA ===');\nconsole.log('Original data from Debug User Lookup:', JSON.stringify(originalData, null, 2));\nconsole.log('Supabase results:', JSON.stringify(supabaseResults, null, 2));\nconsole.log('Is Supabase result an array?', Array.isArray(supabaseResults));\nconsole.log('Supabase array length:', Array.isArray(supabaseResults) ? supabaseResults.length : 'Not an array');\n\n// Create merged result\nconst mergedData = {\n  ...originalData,\n  supabaseResults: Array.isArray(supabaseResults) ? supabaseResults : [supabaseResults],\n  length: Array.isArray(supabaseResults) ? supabaseResults.length : 0,\n  userFound: Array.isArray(supabaseResults) ? supabaseResults.length > 0 : !!supabaseResults\n};\n\nconsole.log('Final merged result:', JSON.stringify(mergedData, null, 2));\nconsole.log('Length property:', mergedData.length);\nconsole.log('=== END MERGE ===');\n\nreturn { json: mergedData };"}, "id": "253b8cd9-d0c9-46d3-8a09-2cefb60a5fa7", "name": "Merge User Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2160, 112]}, {"parameters": {"tableId": "users", "dataToSend": "autoMapInputData"}, "id": "d98415c2-ec50-442f-8d93-f0c31c21e496", "name": "Create New User", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-1936, 768], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"jsCode": "// Mark as new user and pass through the original data\n// Debug: Log the user creation result\nconsole.log('New user created:', JSON.stringify($json, null, 2));\n\nreturn {\n  json: {\n    ...$input.first().json,\n    user: $json[0] || $json,\n    is_new_user: true,\n    debug_user_creation: true\n  }\n};"}, "id": "d208e5fa-6902-4e4e-9424-ee14e5963e67", "name": "<PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1712, 608]}, {"parameters": {"jsCode": "// Mark as existing user and pass through the original data\n// Get original user data from Standardize User Data node\nconst originalData = $('Standardize User Data').item.json;\nconst supabaseUser = $json[0] || $json;\n\nconsole.log('Existing user found:', JSON.stringify(supabaseUser, null, 2));\nconsole.log('Original data:', JSON.stringify(originalData, null, 2));\n\nreturn {\n  json: {\n    ...originalData,\n    user: supabaseUser,\n    is_new_user: false,\n    debug_existing_user: true\n  }\n};"}, "id": "23edb8eb-74d3-4c8a-a80f-fcf19388adb3", "name": "<PERSON> Existing User", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1776, 128]}, {"parameters": {"dataType": "string", "value1": "={{ $json.message ? ($json.message.text.includes(' ') ? $json.message.text.split(' ')[0] : $json.message.text) : ($json.callback_query ? 'callback' : ($json.chat_member ? 'new_member' : '')) }}", "rules": {"rules": [{"value2": "/start"}, {"value2": "show", "output": 1}, {"value2": "/kick", "output": 2}, {"value2": "/pin", "output": 2}, {"value2": "view", "output": 2}, {"value2": "new", "output": 3}, {"value2": "services", "output": 4}, {"value2": "donate", "output": 5}, {"value2": "order", "output": 6}, {"value2": "callback", "output": 7}]}}, "id": "4b22ee2d-37d5-4abd-a4c5-b3e0cfb494aa", "name": "Main Switch", "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [-1504, 320], "notes": "Routes all incoming events. Enhanced to handle payment callbacks."}, {"parameters": {"jsCode": "// Smart Menu Builder - Infers menu type based on context and user state\nconsole.log('=== BUILD MAIN MENU DEBUG ===');\nconsole.log('Input data:', JSON.stringify($json, null, 2));\n\n// Try to get user data from various sources\nlet user = $json.user;\nlet isGuest = $json.is_guest;\nlet isNewUser = $json.is_new_user;\nlet chatId = $json.unifiedChatId || $json.chatId || $('Standardize User Data').item.json.unifiedChatId;\n\n// Fallback: if no user data, try to get from previous nodes\nif (!user) {\n  console.log('No user data found, checking previous nodes...');\n  try {\n    const guestData = $('Guest Mode Handler')?.item?.json;\n    const existingUserData = $('Mark Existing User')?.item?.json;\n    const newUserData = $('Mark New User')?.item?.json;\n    \n    if (guestData?.user) {\n      user = guestData.user;\n      isGuest = true;\n      console.log('Using guest user data');\n    } else if (existingUserData?.user) {\n      user = existingUserData.user;\n      isGuest = false;\n      console.log('Using existing user data');\n    } else if (newUserData?.user) {\n      user = newUserData.user;\n      isGuest = false;\n      isNewUser = true;\n      console.log('Using new user data');\n    }\n  } catch (e) {\n    console.log('Error getting user data from previous nodes:', e.message);\n  }\n}\n\n// Final fallback: create a basic guest user\nif (!user) {\n  console.log('Creating fallback guest user');\n  user = {\n    name: 'Guest',\n    role: 'guest',\n    is_verified_investor: false\n  };\n  isGuest = true;\n}\n\n// Ensure we have a chat ID\nif (!chatId) {\n  chatId = $json.message?.chat?.id || $json.callback_query?.message?.chat?.id || $json.from?.id;\n}\n\nconsole.log('Final user data:', JSON.stringify(user, null, 2));\nconsole.log('Is guest:', isGuest);\nconsole.log('Chat ID:', chatId);\n\nconst isAdmin = user?.role === 'admin';\nisGuest = isGuest || user?.role === 'guest';\n\n// Different welcome messages for different user types\nlet welcomeText;\nlet userStatus;\nlet keyboard;\n\nif (isGuest) {\n  welcomeText = `👋 *Welcome to ELOH Processing DAO*\\n\\n*🏭 Sustainable Crypto Mining in Dominica*\\n\\n🔓 **Browsing as Guest** - Limited Access`;\n  userStatus = '👤 Guest User';\n  \n  // Enhanced guest keyboard with clear guest-specific options\n  keyboard = [\n    [\n      { text: \"🗺️ View Public Roadmap\", callback_data: \"show_roadmap_public\" },\n      { text: \"📊 Live Mining Stats\", callback_data: \"guest_mining_stats\" }\n    ],\n    [\n      { text: \"🔧 Browse Services (Guest)\", callback_data: \"services_menu\" },\n      { text: \"💝 Make Donation\", callback_data: \"donate\" }\n    ],\n    [\n      { text: \"💰 Investment Info (Register Required)\", callback_data: \"register_for_investment\" },\n      { text: \"📈 Why Invest with ELOH?\", callback_data: \"guest_investment_info\" }\n    ],\n    [\n      { text: \"🏭 Operations Center\", url: \"https://elohprocessing.site/operations.php\" },\n      { text: \"📞 Contact Support\", url: \"https://elohprocessing.site/contact.php\" }\n    ],\n    [\n      { text: \"ℹ️ About Registration\", callback_data: \"guest_registration_info\" },\n      { text: \"✅ Create Free Account\", callback_data: \"register_user\" }\n    ],\n    [\n      { text: \"🔄 Continue as Guest\", callback_data: \"guest_continue\" }\n    ]\n  ];\n} else {\n  welcomeText = isNewUser \n    ? `🎉 Welcome to ELOH Processing, ${user?.name || 'User'}!\\n\\n✅ Your account has been created.`\n    : `🚀 *Welcome to ELOH Processing DAO*\\n\\n*🏭 Sustainable Crypto Mining in Dominica*`;\n  \n  userStatus = user?.is_verified_investor ? '💎 Verified Investor' : '👤 Community Member';\n  \n  // Registered user keyboard\n  keyboard = [\n    [\n      { text: \"🗺️ Public Roadmap\", callback_data: \"show_roadmap_public\" },\n      { text: \"💰 My Investment\", callback_data: \"view_investment\" }\n    ],\n    [\n      { text: \"🔧 Our Services\", callback_data: \"services_menu\" },\n      { text: \"💝 Donate\", callback_data: \"donate\" }\n    ],\n    [\n      { text: \"🏭 Operations\", url: \"https://elohprocessing.site/operations.php\" },\n      { text: \"📞 Contact\", url: \"https://elohprocessing.site/contact.php\" }\n    ]\n  ];\n}\n\nconst accessInfo = isGuest \n  ? '*GUEST ACCESS*\\n🗺️ View Roadmap | 🔧 Browse Services | 💝 Donate\\n\\n*REGISTER TO UNLOCK*\\n🔐 Investment Portal | 📊 Portfolio | 💎 Investment Tiers'\n  : '*PUBLIC ACCESS*\\n🗺️ View Roadmap | 📈 Live Metrics | 🏭 Operations\\n\\n*INVESTOR PORTAL*\\n🔐 Check Investment | 📊 Portfolio | 💎 Investment Tiers';\n\nconst result = {\n  chatId: chatId,\n  text: `${welcomeText}\\n\\n*Your Profile:*\\n• Status: ${userStatus}\\n\\n${accessInfo}\\n\\n*SERVICES*\\n🔧 Mining ($500/mo) | ⛏️ Pool ($200/yr) | 📊 Consulting ($150/hr)\\n\\n*PAYMENTS*\\n💰 Donate | 🔧 Pay Services | ⚡ Lightning/On-chain`,\n  reply_markup: {\n    inline_keyboard: keyboard\n  },\n  parse_mode: \"Markdown\",\n  messageId: $json.isCallback ? $json.callback_query?.message?.message_id : undefined\n};\n\nconsole.log('Menu result:', JSON.stringify(result, null, 2));\nconsole.log('=== END BUILD MAIN MENU DEBUG ===');\n\nreturn result;"}, "id": "1abfb087-b3dc-43fa-baf8-a2b523952582", "name": "Build Main Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1008, 112]}, {"parameters": {"chatId": "={{ $json.chatId || $('Standardize User Data').item.json.unifiedChatId || $json.unifiedChatId || $json.message?.chat?.id || $json.callback_query?.message?.chat?.id || $json.from?.id }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "forceReply": {}, "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {}}, "id": "ea9e8d6c-c917-4b6a-bb5b-1fc73bffa53d", "name": "Send Main Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-464, 64], "webhookId": "f7808a34-8de2-4cca-9313-c1ec353740df", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "get", "tableId": "project_roadmap", "filters": {"conditions": []}}, "id": "c5e3f3d4-08d2-498b-87d8-4b85e7609806", "name": "Get Roadmap Data", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-704, 256], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"jsCode": "const items = $input.all();\nlet formattedText = '🗺️ **ELOH Processing Public Roadmap**\\n\\n';\nfor (const item of items) {\n  formattedText += `- **${item.json.name}**: ${item.json.status}\\n`;\n}\n\n$input.first().json.formatted_text = formattedText;\nreturn $input.first();"}, "id": "8d7a88ca-a234-4d40-a3a1-dbf2c7863b34", "name": "Format Roadmap Text", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-480, 272]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "={{ $json.formatted_text }}", "additionalFields": {}}, "id": "6be53f2e-9993-4d16-a26e-c1192edfc319", "name": "Send Roadmap", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-224, 176], "webhookId": "cf66780a-dbcf-441d-aa1b-e62ffe47170e", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "=Welcome @{{ $json.unifiedUsername }} to ELOH Processing DAO! 🎉\n\nPlease review our rules and start a private chat for investor features.", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "💬 Start Private Chat", "additionalFields": {"url": "={{ 'https://t.me/' + $node['Set Variables'].json.botUsername }}"}}]}}]}, "additionalFields": {}}, "id": "11c0e4a6-f1da-419e-bdb5-9dfc9d0593b8", "name": "Send Welcome", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1200, 112], "webhookId": "04b66bdf-bcbc-4144-b12f-6b25d86e4b0c", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "get", "tableId": "users", "filters": {"conditions": [{"keyName": "telegram_id", "keyValue": "="}]}}, "id": "7f830d25-b58f-461f-8521-b31de588a763", "name": "Check User in Database", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-848, 528], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"chatId": "={{ $json.chatId || $json.unifiedChatId || $('Standardize User Data').item.json.unifiedChatId }}", "text": "=Hello {{ $json.user?.name || 'Investor' }}! Your current investment value is: ${{ $json.user?.investment_details?.total_value_usd || '0' }}.", "additionalFields": {}}, "id": "72c72308-ac3a-4a01-9bab-454a08c98ae2", "name": "Send Investment Details", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [336, 736], "webhookId": "3fd54c83-3909-4de6-a53c-34c37f734e3b", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "Your Telegram account is not yet linked to a verified investor profile. Please contact support.", "additionalFields": {}}, "id": "93960ab3-31d0-4a85-a3ba-9985d2e51991", "name": "Send Not Verified Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [576, 864], "webhookId": "e797834f-b962-4a70-ad7c-7d45762ad3aa", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "const user = $json.user;\nconst isGuest = $json.is_guest || user?.role === 'guest';\n\n// Different service presentation for guests vs registered users\nlet serviceButtons;\nlet headerText;\nlet footerText = '';\n\nif (isGuest) {\n  headerText = `🔧 *ELOH Processing Services*\\n\\n🔓 **Browsing as Guest** - Registration required for purchases\\n\\nExplore our professional services:`;\n  \n  serviceButtons = [\n    [{ text: \"🔍 Mining Services - $500/mo (View Details)\", callback_data: \"guest_service_mining\" }],\n    [{ text: \"� Pool Membership - $200/yr (View Details)\", callback_data: \"guest_service_pool\" }],\n    [{ text: \"🔍 Consulting - $150/hr (View Details)\", callback_data: \"guest_service_consulting\" }],\n    [{ text: \"🔍 Analysis Report - $99 (View Details)\", callback_data: \"guest_service_analysis\" }]\n  ];\n  \n  footerText = '\\n\\n🔐 *Register to purchase services and unlock member pricing*';\n} else {\n  headerText = `🔧 *ELOH Processing Services*\\n\\nLeverage our expertise and infrastructure:`;\n  \n  serviceButtons = [\n    [{ text: \"�💳 Mining Services - $500/mo\", callback_data: \"order_mining_500\" }],\n    [{ text: \"💳 Pool Membership - $200/yr\", callback_data: \"order_pool_200\" }],\n    [{ text: \"💳 Consulting - $150/hr\", callback_data: \"order_consulting_150\" }],\n    [{ text: \"💳 Analysis Report - $99\", callback_data: \"order_analysis_99\" }]\n  ];\n}\n\nconst additionalButtons = isGuest ? [\n  [{ text: \"✅ Register to Purchase\", callback_data: \"register_user\" }]\n] : [];\n\nconst keyboard = [...serviceButtons, ...additionalButtons, [\n  { text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }\n]];\n\nconst serviceDetails = `\\n\\n• **Mining Operations**: $500/month - 24/7 sustainable ASIC mining\\n• **Mining Pool Membership**: $200/year - Join our transparent pool\\n• **Strategy Consulting**: $150/hour - Expert crypto & forex guidance\\n• **Market Analysis Report**: $99/report - Detailed market insights`;\n\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `${headerText}${serviceDetails}${footerText}\\n\\nSelect a service below to ${isGuest ? 'learn more:' : 'proceed:'}`,\n  reply_markup: {\n    inline_keyboard: keyboard\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.isCallback ? $json.callback_query.message.message_id : undefined\n};"}, "id": "a680b956-c71f-4fa5-90e5-64858e80f7c0", "name": "Build Services Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-992, 912]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "forceReply": {}, "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {}}, "id": "c4012a7e-42da-4ada-b006-8d4b6e959b36", "name": "Send Services Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-736, 912], "webhookId": "ce4c0427-72a9-451e-aa40-4b9dac6bcbf8", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "const user = $json.user;\n\nconst donateButtons = [\n  [{ text: \"$5 - Support ELOH\", callback_data: \"donate_5\" }],\n  [{ text: \"$10 - Fuel Operations\", callback_data: \"donate_10\" }],\n  [{ text: \"$25 - Expand Infrastructure\", callback_data: \"donate_25\" }],\n  [{ text: \"Custom Amount\", callback_data: \"donate_custom\" }]\n];\n\nconst keyboard = [...donateButtons, [\n  { text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }\n]];\n\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `💝 *Support ELOH Processing DAO*\\n\\nYour generous contributions help us maintain and expand our sustainable crypto mining operations in Dominica. Every donation, big or small, makes a difference!\\n\\nChoose a preset amount or enter a custom amount:`,\n  reply_markup: {\n    inline_keyboard: keyboard\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.isCallback ? $json.callback_query.message.message_id : undefined\n};"}, "id": "9d59fa4a-41da-4293-9d02-b0ac930f9c4b", "name": "Build Donate Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-880, 1056]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "forceReply": {}, "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {}}, "id": "d3638dde-ea3f-4c00-835f-cbe26d13838a", "name": "Send Donate <PERSON>u", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-688, 1056], "webhookId": "1cca6b01-d29b-4f44-95fc-73fa22064cac", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "Sorry, I didn't understand that. Please use the menu buttons.", "additionalFields": {}}, "id": "2baa515d-5cc4-48a0-9c23-af8a318f807c", "name": "Unhandled Input", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1696, 816], "webhookId": "54a07ea0-e42b-49cd-8ad1-57700d90dc54", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "get", "tableId": "users", "filters": {"conditions": [{"keyName": "telegram_id"}, {"keyName": "role"}]}}, "id": "a8822355-1f32-4b6c-aee9-bffccb3ad02c", "name": "Check if User is Admin1", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-640, 592], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "Admin command received. This feature is coming soon!", "additionalFields": {}}, "id": "0b910689-6085-40d7-bea1-d180bc23398f", "name": "Admin Action Placeholder", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [160, 384], "webhookId": "5b9eb8ba-07ba-49b3-9f01-81b6def74252", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.chatId || $json.unifiedChatId || $('Standardize User Data').item.json.unifiedChatId }}", "text": "=Hello {{ $json.user?.name || 'Investor' }}! Your current investment value is: ${{ $json.user?.investment_details?.total_value_usd || '0' }}.", "additionalFields": {}}, "id": "5a8f63d8-36c1-4627-a368-96a6b34e5f08", "name": "Send Investment Details1", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [256, 544], "webhookId": "2a151dcc-fc11-477a-accf-388dd945cf76", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId || $json.message?.chat?.id || $json.callback_query?.message?.chat?.id }}", "text": "❌ *Invalid User Data*\n\nSorry, we couldn't process your request due to invalid user information. This might happen if:\n\n• Your user ID is missing or corrupted\n• There's a temporary data issue\n• The bot needs to be restarted\n\n🔄 Please try using /start to restart the bot.\n📞 If the problem persists, contact support.", "additionalFields": {}}, "id": "698a5c7c-18b9-434e-a78e-bb649b23e8e6", "name": "Invalid User E<PERSON><PERSON>", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-2288, 672], "webhookId": "dcfd838e-0704-44c5-a6f5-3c50e6fac1bf", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId || $json.message?.chat?.id || $json.callback_query?.message?.chat?.id }}", "text": "🔧 *Database Connection Error*\n\nWe're experiencing temporary technical difficulties with our database. Please try again in a few moments.\n\n⏳ This is usually resolved quickly\n🔄 Use /start to try again\n📞 Contact support if the issue persists", "additionalFields": {}}, "id": "7cff3cd3-66e2-419b-9481-6c2ea056492b", "name": "Database Error <PERSON>", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1936, 560], "webhookId": "7f250e6b-0761-43ec-b6a9-3aa81a7d9943", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a4b559b8-ff00-40c1-9af6-25320ae40940", "leftValue": "={{ $json.supabaseResults[0] }}", "rightValue": "", "operator": {"type": "object", "operation": "notEmpty", "singleValue": true}}], "combinator": "or"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1984, 32], "id": "11001f91-f585-4551-8688-9adddf1e8437", "name": "User Check"}, {"parameters": {"jsCode": "// Guest Mode Handler - Allow users to browse without registration\n// but prompt them to register for investment features\n\nconst originalData = $('Standardize User Data').item.json;\n\nconsole.log('=== GUEST MODE ACTIVATED ===');\nconsole.log('User ID:', originalData.unifiedUserId);\nconsole.log('Username:', originalData.unifiedUsername);\nconsole.log('First Name:', originalData.unifiedFirstName);\n\n// Create a guest user object with limited permissions\nconst guestUser = {\n  telegram_id: originalData.unifiedUserId,\n  username: originalData.unifiedUsername || '',\n  name: originalData.unifiedFirstName || 'Guest',\n  is_verified_investor: false,\n  role: 'guest',\n  is_guest: true,\n  created_at: new Date().toISOString()\n};\n\nconst mergedData = {\n  ...originalData,\n  user: guestUser,\n  is_new_user: false,\n  is_guest: true,\n  debug_guest_mode: true\n};\n\nconsole.log('Guest user created:', JSON.stringify(guestUser, null, 2));\nconsole.log('=== END GUEST MODE ===');\n\nreturn { json: mergedData };"}, "id": "7edecd35-5f5e-46ab-a5be-d2381c66bfed", "name": "Guest <PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2112, 448], "disabled": true}, {"parameters": {"jsCode": "// Order Handler - Process order requests\nconst user = $json.user;\nconst isGuest = $json.is_guest || user?.role === 'guest';\n\n// Extract order details from callback data\nconst callbackData = $json.callback_query?.data || '';\nconst orderMatch = callbackData.match(/order_(.+)_(\\d+)/);\n\nif (!orderMatch) {\n  return {\n    chatId: $json.unifiedChatId,\n    text: \"❌ Invalid order request. Please try again.\",\n    reply_markup: {\n      inline_keyboard: [[\n        { text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }\n      ]]\n    },\n    parse_mode: 'Markdown'\n  };\n}\n\nconst [, serviceName, price] = orderMatch;\nconst serviceDisplayName = serviceName.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase());\n\n// For guests, show registration prompt\nif (isGuest) {\n  return {\n    chatId: $json.unifiedChatId,\n    text: `🔐 *Registration Required*\\n\\nTo order **${serviceDisplayName}** ($${price}), you need to register first.\\n\\n✅ Registration is quick and free\\n💎 Unlock investment features\\n🔧 Access all services\\n\\nWould you like to register now?`,\n    reply_markup: {\n      inline_keyboard: [\n        [{ text: \"✅ Register Now\", callback_data: \"register_user\" }],\n        [{ text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }]\n      ]\n    },\n    parse_mode: 'Markdown',\n    messageId: $json.callback_query?.message?.message_id\n  };\n}\n\n// For registered users, proceed with order\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `🛒 *Order Confirmation*\\n\\n**Service:** ${serviceDisplayName}\\n**Price:** $${price}\\n\\nProceed with payment?`,\n  reply_markup: {\n    inline_keyboard: [\n      [{ text: \"💳 Pay Now\", callback_data: `pay_${serviceName}_${price}` }],\n      [{ text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }]\n    ]\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.callback_query?.message?.message_id\n};"}, "id": "5cdea959-3658-443d-8221-30b6bc68cf1b", "name": "Order Handler", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-496, 1056]}, {"parameters": {"chatId": "={{ $json.chatId || $json.unifiedChatId || $('Standardize User Data').item.json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "forceReply": {}, "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {}}, "id": "8e284b0b-7e0a-4c77-8ea2-b63de3af6bbf", "name": "Send Order Response", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-240, 1056], "webhookId": "order-response-webhook", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"dataType": "string", "value1": "={{ $json.callback_query ? $json.callback_query.data : 'no_callback' }}", "rules": {"rules": [{"value2": "main_menu"}, {"value2": "services_menu", "output": 1}, {"value2": "donate", "output": 2}, {"value2": "register_user", "output": 3}, {"value2": "register_for_investment", "output": 4}, {"value2": "view_investment", "output": 5}, {"value2": "confirm_registration", "output": 6}, {"value2": "donate_5", "output": 7}, {"value2": "donate_10", "output": 7}, {"value2": "donate_25", "output": 7}, {"value2": "donate_custom", "output": 7}, {"value2": "guest_mining_stats", "output": 8}, {"value2": "guest_investment_info", "output": 9}, {"value2": "guest_registration_info", "output": 10}, {"value2": "guest_continue", "output": 11}, {"value2": "show_roadmap_public", "output": 12}]}}, "id": "8eaae296-6e3c-413f-ab0d-12555fb78952", "name": "Callback Router", "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [368, 1072]}, {"parameters": {"jsCode": "// User Registration Handler\nconst user = $json.user;\nconst isGuest = $json.is_guest || user?.role === 'guest';\n\nif (!isGuest) {\n  return {\n    chatId: $json.unifiedChatId,\n    text: \"✅ You're already registered! Use the menu to access all features.\",\n    reply_markup: {\n      inline_keyboard: [[\n        { text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }\n      ]]\n    },\n    parse_mode: 'Markdown',\n    messageId: $json.callback_query?.message?.message_id\n  };\n}\n\n// For guests, show registration form\nreturn {\n  chatId: $json.unifiedChatId,\n  text: \"📝 *Account Registration*\\n\\nTo create your account, we'll use your Telegram information.\\n\\n**Benefits of Registration:**\\n✅ Access investment features\\n💎 Portfolio tracking\\n🔧 Full service access\\n📊 Personalized dashboard\\n\\nProceed with registration?\",\n  reply_markup: {\n    inline_keyboard: [\n      [{ text: \"✅ Create Account\", callback_data: \"confirm_registration\" }],\n      [{ text: \"❌ Cancel\", callback_data: \"main_menu\" }]\n    ]\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.callback_query?.message?.message_id\n};"}, "id": "540cde55-e70f-480c-9178-47810dbea0e7", "name": "Registration Handler", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-496, 1280]}, {"parameters": {"chatId": "={{ $json.chatId || $json.unifiedChatId || $('Standardize User Data').item.json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "forceReply": {}, "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {}}, "id": "ad847958-925d-4c6e-8318-dd891c1dbc1d", "name": "Send Registration Response", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-240, 1280], "webhookId": "registration-response-webhook", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "insert"}, "id": "c500cc35-9488-4f3c-a1f0-99c2d3650eed", "name": "Create Registered User", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-240, 1360], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"jsCode": "// Registration Success Handler\nconst user = $json;\n\nconsole.log('User registration completed:', JSON.stringify(user, null, 2));\n\nreturn {\n  chatId: $('Standardize User Data').item.json.unifiedChatId,\n  text: `🎉 *Registration Successful!*\\n\\nWelcome to ELOH Processing DAO, ${user.name}!\\n\\n✅ Your account has been created\\n💎 You can now access investment features\\n🔧 Full service access unlocked\\n\\nUse /start to access the main menu with all features.`,\n  reply_markup: {\n    inline_keyboard: [[\n      { text: \"🏠 Go to Main Menu\", callback_data: \"main_menu\" }\n    ]]\n  },\n  parse_mode: 'Markdown'\n};"}, "id": "aa9aa87b-e42f-4c53-9b54-e95c46608137", "name": "Registration Success Handler", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [16, 1360]}, {"parameters": {"chatId": "={{ $json.chatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "forceReply": {}, "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {}}, "id": "a0656a78-53e1-4635-9f5a-bc2f79f2860e", "name": "Send Registration Success", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [272, 1360], "webhookId": "registration-success-webhook", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "// Donation Processor - Handle donation amount selection\nconst user = $json.user;\nconst isGuest = $json.is_guest || user?.role === 'guest';\nconst callbackData = $json.callback_query?.data || '';\n\n// Extract donation amount\nlet amount = 0;\nif (callbackData.includes('donate_')) {\n  const amountMatch = callbackData.match(/donate_(\\d+|custom)/);\n  if (amountMatch) {\n    amount = amountMatch[1] === 'custom' ? 'custom' : parseInt(amountMatch[1]);\n  }\n}\n\nif (amount === 'custom') {\n  return {\n    chatId: $json.unifiedChatId,\n    text: \"💝 *Custom Donation Amount*\\n\\nPlease enter your desired donation amount in USD (minimum $1):\",\n    reply_markup: {\n      inline_keyboard: [[\n        { text: \"❌ Cancel\", callback_data: \"donate\" }\n      ]]\n    },\n    parse_mode: 'Markdown',\n    messageId: $json.callback_query?.message?.message_id\n  };\n}\n\nif (amount > 0) {\n  const guestMessage = isGuest ? '\\n\\n🔓 *Note:* You\\'re donating as a guest. Consider registering for donation tracking and tax receipts.' : '';\n  \n  return {\n    chatId: $json.unifiedChatId,\n    text: `💝 *Donation Confirmation*\\n\\n**Amount:** $${amount}\\n**Purpose:** Support ELOH Processing DAO\\n\\nYour contribution helps fund our sustainable crypto mining operations in Dominica.${guestMessage}\\n\\nProceed with payment?`,\n    reply_markup: {\n      inline_keyboard: [\n        [{ text: \"💳 Donate Now\", callback_data: `pay_donation_${amount}` }],\n        [{ text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }]\n      ]\n    },\n    parse_mode: 'Markdown',\n    messageId: $json.callback_query?.message?.message_id\n  };\n}\n\nreturn {\n  chatId: $json.unifiedChatId,\n  text: \"❌ Invalid donation amount. Please try again.\",\n  reply_markup: {\n    inline_keyboard: [[\n      { text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }\n    ]]\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.callback_query?.message?.message_id\n};"}, "id": "42a4e9f9-c45c-4121-a99c-333aa60b4097", "name": "Donation Processor", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-496, 1440]}, {"parameters": {"chatId": "={{ $json.chatId || $json.unifiedChatId || $('Standardize User Data').item.json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "forceReply": {}, "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {}}, "id": "4469a1f5-e88d-4cce-91d3-4ffcc83cb353", "name": "Send Donation Response", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-16, 1504], "webhookId": "donation-response-webhook", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "// Investment Registration Prompt - Handle guest users trying to access investment features\nconst user = $json.user;\nconst isGuest = $json.is_guest || user?.role === 'guest';\n\nif (isGuest) {\n  return {\n    chatId: $json.unifiedChatId,\n    text: \"🔐 *Investment Portal Access*\\n\\nTo access investment features, you need to register an account first.\\n\\n**Investment Features Include:**\\n💎 Portfolio tracking\\n📊 Investment dashboard\\n📈 Performance analytics\\n🔔 Investment notifications\\n💰 Dividend tracking\\n\\n**Registration Benefits:**\\n✅ Free account creation\\n🔒 Secure data protection\\n📱 Full bot access\\n🎯 Personalized experience\\n\\nWould you like to register now?\",\n    reply_markup: {\n      inline_keyboard: [\n        [{ text: \"✅ Register for Investment Access\", callback_data: \"register_user\" }],\n        [{ text: \"ℹ️ Learn More About Registration\", callback_data: \"registration_info\" }],\n        [{ text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }]\n      ]\n    },\n    parse_mode: 'Markdown',\n    messageId: $json.callback_query?.message?.message_id\n  };\n}\n\n// For registered users, redirect to investment check\nreturn {\n  redirect: 'investment_check',\n  ...($json)\n};"}, "id": "aae16071-eece-4a0f-8692-af04e46f5d82", "name": "Investment Registration Prompt", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-496, 1520]}, {"parameters": {"chatId": "={{ $json.chatId || $json.unifiedChatId || $('Standardize User Data').item.json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "forceReply": {}, "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {}}, "id": "ca64b0f6-3d34-4d1f-ad97-e4b784bc34fb", "name": "Send Investment Prompt", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-240, 1520], "webhookId": "investment-prompt-webhook", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "// Guest Mining Stats - Show public mining statistics\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `📊 *ELOH Processing - Live Mining Stats*\\n\\n🔓 **Public Statistics** (Guest View)\\n\\n⚡ **Current Operations:**\\n• Active Miners: 24 ASIC units\\n• Hash Rate: ~2.4 PH/s\\n• Power Consumption: 100% renewable\\n• Uptime: 99.2%\\n\\n🌍 **Sustainability:**\\n• Location: Dominica (Caribbean)\\n• Energy: 100% geothermal & hydroelectric\\n• Carbon Footprint: Net negative\\n• Environmental Impact: Minimal\\n\\n💰 **Performance (Last 30 Days):**\\n• Blocks Mined: 12\\n• Revenue Generated: $18,500\\n• Operating Efficiency: 94%\\n• Pool Contribution: 2.1%\\n\\n🔐 *Register for detailed analytics and investment opportunities*`,\n  reply_markup: {\n    inline_keyboard: [\n      [{ text: \"📈 View Full Analytics (Register)\", callback_data: \"register_for_investment\" }],\n      [{ text: \"💰 Investment Opportunities\", callback_data: \"guest_investment_info\" }],\n      [{ text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }]\n    ]\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.callback_query?.message?.message_id\n};"}, "id": "947e5592-2d54-46c1-b84f-958b5b805762", "name": "Guest Mining Stats", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-880, 1488]}, {"parameters": {"jsCode": "// Guest Investment Info - Explain investment opportunities\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `💎 *Why Invest with ELOH Processing?*\\n\\n🔓 **Guest Information**\\n\\n🏭 **Our Mission:**\\nSustainable cryptocurrency mining in the Caribbean using 100% renewable energy sources.\\n\\n💰 **Investment Highlights:**\\n• Minimum Investment: $1,000\\n• Expected ROI: 15-25% annually\\n• Dividend Payments: Monthly\\n• Transparency: Full operational visibility\\n• Sustainability: Carbon-negative operations\\n\\n🌟 **Investor Benefits:**\\n• Priority access to new mining capacity\\n• Quarterly performance reports\\n• Voting rights on major decisions\\n• Tax-optimized structure\\n• Liquidity options available\\n\\n📊 **Track Record:**\\n• 3+ years of profitable operations\\n• 99%+ uptime reliability\\n• Consistent dividend payments\\n• Growing mining capacity\\n\\n🔐 *Register now to access detailed investment materials and start your journey*`,\n  reply_markup: {\n    inline_keyboard: [\n      [{ text: \"✅ Register to Invest\", callback_data: \"register_user\" }],\n      [{ text: \"📊 View Mining Stats\", callback_data: \"guest_mining_stats\" }],\n      [{ text: \"📞 Contact Investment Team\", url: \"https://elohprocessing.site/contact.php\" }],\n      [{ text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }]\n    ]\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.callback_query?.message?.message_id\n};"}, "id": "1344bd78-3fea-4fe8-a6d1-d641fbd8ad0c", "name": "Guest Investment Info", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-880, 1616]}, {"parameters": {"jsCode": "// Guest Registration Info - Explain registration benefits\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `ℹ️ *About ELOH Processing Registration*\\n\\n🔓 **Currently Browsing as Guest**\\n\\n✅ **Registration Benefits:**\\n\\n🔐 **Investment Access:**\\n• View detailed portfolio analytics\\n• Track investment performance\\n• Receive dividend notifications\\n• Access investor-only reports\\n\\n🎯 **Personalized Experience:**\\n• Customized dashboard\\n• Personal investment advisor\\n• Priority customer support\\n• Early access to new opportunities\\n\\n🔒 **Security & Privacy:**\\n• Secure account protection\\n• Encrypted data storage\\n• Two-factor authentication\\n• GDPR compliant\\n\\n💼 **Professional Features:**\\n• Tax reporting assistance\\n• Investment history tracking\\n• Performance benchmarking\\n• Portfolio optimization tools\\n\\n🆓 **Registration is completely FREE and takes less than 30 seconds!**\\n\\n*We only use your Telegram information - no additional data required.*`,\n  reply_markup: {\n    inline_keyboard: [\n      [{ text: \"✅ Register Now (Free)\", callback_data: \"register_user\" }],\n      [{ text: \"💰 Learn About Investing\", callback_data: \"guest_investment_info\" }],\n      [{ text: \"🔄 Continue as Guest\", callback_data: \"guest_continue\" }],\n      [{ text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }]\n    ]\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.callback_query?.message?.message_id\n};"}, "id": "9079fd2b-1b50-4f5e-b13c-c58e73c5e45a", "name": "Guest Registration Info", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-912, 1760]}, {"parameters": {"jsCode": "// Guest Continue Handler - Acknowledge guest choice and show limited features\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `🔄 *Continuing as Guest*\\n\\n👤 **Guest Access Confirmed**\\n\\nYou can continue using ELOH Processing with limited access. Here's what you can do:\\n\\n✅ **Available Features:**\\n• View public roadmap\\n• Browse services (with registration prompts)\\n• Make donations\\n• View mining statistics\\n• Access public information\\n• Contact support\\n\\n🔒 **Requires Registration:**\\n• Investment portfolio access\\n• Detailed analytics\\n• Service purchases\\n• Dividend tracking\\n• Priority support\\n\\n💡 **Tip:** You can register at any time by using the \\\"Register Account\\\" button in the main menu.\\n\\n*Registration is free and unlocks all features instantly!*`,\n  reply_markup: {\n    inline_keyboard: [\n      [{ text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }],\n      [{ text: \"✅ Actually, Let's Register\", callback_data: \"register_user\" }]\n    ]\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.callback_query?.message?.message_id\n};"}, "id": "4aa59807-3fbc-4e96-8acc-d4405375aef4", "name": "Guest Continue <PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-992, 1904]}, {"parameters": {"chatId": "={{ $json.chatId || $json.unifiedChatId || $('Standardize User Data').item.json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "forceReply": {}, "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {}}, "id": "392e45ee-0cb2-42e8-9c03-06f94f9dced4", "name": "Send Guest Mining Stats", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-240, 1600], "webhookId": "guest-mining-stats-webhook", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.chatId || $json.unifiedChatId || $('Standardize User Data').item.json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "forceReply": {}, "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {}}, "id": "6a162204-8446-48f8-94c3-a47d16117de4", "name": "Send Guest Investment Info", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-240, 1680], "webhookId": "guest-investment-info-webhook", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.chatId || $json.unifiedChatId || $('Standardize User Data').item.json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "forceReply": {}, "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {}}, "id": "174a5b40-c2ed-4c40-9e59-6dddd33c4ea2", "name": "Send Guest Registration Info", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-240, 1776], "webhookId": "guest-registration-info-webhook", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.chatId || $json.unifiedChatId || $('Standardize User Data').item.json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "forceReply": {}, "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {}}, "id": "9948d405-4b97-4db1-a191-e1c77388d43c", "name": "Send Guest Continue", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-240, 1856], "webhookId": "guest-continue-webhook", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b2dd2aca-bca3-4d2e-bab1-8eb1c29cc228", "leftValue": "={{ $('Merge User Data').item.json.supabaseResults[0].role }}", "rightValue": "={{ $('Merge User Data').item.json.supabaseResults[0].role }}", "operator": {"type": "string", "operation": "equals"}}, {"id": "68e03373-512c-4b91-a1e3-8658a4a7e05c", "leftValue": "={{ $('Merge User Data').item.json.supabaseResults[0].role }}", "rightValue": "\"\"", "operator": {"type": "string", "operation": "empty", "singleValue": true}}], "combinator": "or"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-624, 448], "id": "873afbe2-cb17-49e8-9bfb-1e5047661334", "name": "If role"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "4f9d987e-e837-46a6-9497-77e3f47592da", "leftValue": "={{ $json.role }}", "rightValue": "admin", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}, {"id": "ed6d0ee3-4494-4724-a89f-91073ebef6d2", "leftValue": "={{ $json.role }}", "rightValue": "superuser", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "or"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-288, 352], "id": "96932661-a80d-4861-abc6-8007e221ac84", "name": "If ADMIN"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "4f9d987e-e837-46a6-9497-77e3f47592da", "leftValue": "={{ $json.role }}", "rightValue": "user", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-64, 1040], "id": "987b06b0-57ba-4a90-bd0b-85a8d9ffcd4e", "name": "If GUEST"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "4f9d987e-e837-46a6-9497-77e3f47592da", "leftValue": "={{ $json.role }}", "rightValue": "member", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-112, 704], "id": "98aff406-ebc3-4305-b5ec-2fff696c0046", "name": "If MEMBER"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "4f9d987e-e837-46a6-9497-77e3f47592da", "leftValue": "={{ $json.role }}", "rightValue": "investor", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-240, 576], "id": "56d3e89f-99ef-437f-b815-e3ec03876891", "name": "If INVESTOR"}, {"parameters": {"jsCode": "// Smart Menu Builder - Build main menu based on user type\nconst user = $json.user;\nconst isGuest = $json.is_guest || user?.role === 'guest';\nconst chatId = $json.unifiedChatId;\n\nlet welcomeText;\nlet keyboard;\n\nif (isGuest) {\n  welcomeText = `👋 *Welcome to ELOH Processing DAO*\\n\\n*🏭 Sustainable Crypto Mining in Dominica*\\n\\n🔓 **Browsing as Guest** - Limited Access`;\n  \n  keyboard = [\n    [\n      { text: \"🗺️ View Public Roadmap\", callback_data: \"show_roadmap_public\" },\n      { text: \"📊 Live Mining Stats\", callback_data: \"guest_mining_stats\" }\n    ],\n    [\n      { text: \"🔧 Browse Services (Guest)\", callback_data: \"services_menu\" },\n      { text: \"💝 Make Donation\", callback_data: \"donate\" }\n    ],\n    [\n      { text: \"💰 Investment Info\", callback_data: \"guest_investment_info\" },\n      { text: \"✅ Create Free Account\", callback_data: \"register_user\" }\n    ]\n  ];\n} else {\n  welcomeText = `🚀 *Welcome to ELOH Processing DAO*\\n\\n*🏭 Sustainable Crypto Mining in Dominica*\\n\\nHello ${user?.name || 'User'}!`;\n  \n  keyboard = [\n    [\n      { text: \"🗺️ Public Roadmap\", callback_data: \"show_roadmap_public\" },\n      { text: \"💰 My Investment\", callback_data: \"view_investment\" }\n    ],\n    [\n      { text: \"🔧 Our Services\", callback_data: \"services_menu\" },\n      { text: \"💝 Donate\", callback_data: \"donate\" }\n    ]\n  ];\n}\n\nreturn {\n  chatId: chatId,\n  text: welcomeText,\n  reply_markup: {\n    inline_keyboard: keyboard\n  },\n  parse_mode: \"Markdown\",\n  messageId: $json.isCallback ? $json.callback_query?.message?.message_id : undefined\n};"}, "id": "95d47e51-7c3f-4d4f-bc7c-475a5edc9eba", "name": "Build Main Menu1", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1200, 352]}, {"parameters": {"jsCode": "// Create guest user for browsing\nconst originalData = $('Standardize User Data').item.json;\n\nconst guestUser = {\n  telegram_id: originalData.unifiedUserId,\n  username: originalData.unifiedUsername || '',\n  name: originalData.unifiedFirstName || 'Guest',\n  role: 'guest',\n  is_verified_investor: false\n};\n\nreturn {\n  json: {\n    ...originalData,\n    user: guestUser,\n    is_new_user: false,\n    is_guest: true\n  }\n};"}, "id": "c676a92e-3d7d-4cba-896f-a34436d07f9b", "name": "Guest Mode Handler1", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1008, 1200]}], "pinData": {}, "connections": {"Telegram Trigger": {"main": [[{"node": "Set Variables", "type": "main", "index": 0}]]}, "Set Variables": {"main": [[{"node": "Test Supabase Connection", "type": "main", "index": 0}]]}, "Standardize User Data": {"main": [[{"node": "Event Router", "type": "main", "index": 0}]]}, "Event Router": {"main": [[{"node": "Validate User ID", "type": "main", "index": 0}]]}, "Create New User": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}, {"node": "Database Error <PERSON>", "type": "main", "index": 0}]]}, "Mark New User": {"main": [[{"node": "Main Switch", "type": "main", "index": 0}]]}, "Main Switch": {"main": [[{"node": "Send Welcome", "type": "main", "index": 0}], [{"node": "Get Roadmap Data", "type": "main", "index": 0}], [{"node": "Check User in Database", "type": "main", "index": 0}], [{"node": "Send Welcome", "type": "main", "index": 0}], [{"node": "Build Services Menu", "type": "main", "index": 0}], [{"node": "Build Donate Menu", "type": "main", "index": 0}], [{"node": "Order Handler", "type": "main", "index": 0}], [{"node": "Callback Router", "type": "main", "index": 0}]]}, "Send Welcome": {"main": [[{"node": "Build Main Menu", "type": "main", "index": 0}]]}, "Build Main Menu": {"main": [[{"node": "Send Main Menu", "type": "main", "index": 0}]]}, "Mark Existing User": {"main": [[{"node": "Main Switch", "type": "main", "index": 0}]]}, "Get Roadmap Data": {"main": [[{"node": "Format Roadmap Text", "type": "main", "index": 0}]]}, "Format Roadmap Text": {"main": [[{"node": "Send Roadmap", "type": "main", "index": 0}]]}, "Build Services Menu": {"main": [[{"node": "Send Services Menu", "type": "main", "index": 0}]]}, "Build Donate Menu": {"main": [[{"node": "Send Donate <PERSON>u", "type": "main", "index": 0}]]}, "Check User in Database": {"main": [[{"node": "If role", "type": "main", "index": 0}]]}, "Send Main Menu": {"main": [[{"node": "Check User in Database", "type": "main", "index": 0}]]}, "Check Existing User": {"main": [[{"node": "Merge User Data", "type": "main", "index": 0}]]}, "Merge User Data": {"main": [[{"node": "User Check", "type": "main", "index": 0}]]}, "Test Supabase Connection": {"main": [[{"node": "Standardize User Data", "type": "main", "index": 0}]]}, "Validate User ID": {"main": [[{"node": "Debug Before User Lookup", "type": "main", "index": 0}]]}, "Debug Before User Lookup": {"main": [[{"node": "Check Existing User", "type": "main", "index": 0}]]}, "User Check": {"main": [[{"node": "<PERSON> Existing User", "type": "main", "index": 0}], [{"node": "Guest <PERSON>", "type": "main", "index": 0}]]}, "Guest Mode Handler": {"main": [[{"node": "Main Switch", "type": "main", "index": 0}]]}, "Order Handler": {"main": [[{"node": "Send Order Response", "type": "main", "index": 0}]]}, "Callback Router": {"main": [[{"node": "Build Main Menu1", "type": "main", "index": 0}], [{"node": "Build Services Menu", "type": "main", "index": 0}], [{"node": "Build Donate Menu", "type": "main", "index": 0}], [{"node": "Registration Handler", "type": "main", "index": 0}], [{"node": "Investment Registration Prompt", "type": "main", "index": 0}], [{"node": "Send Investment Details", "type": "main", "index": 0}], [{"node": "Create Registered User", "type": "main", "index": 0}], [{"node": "Donation Processor", "type": "main", "index": 0}], [{"node": "Guest Mining Stats", "type": "main", "index": 0}], [{"node": "Guest Investment Info", "type": "main", "index": 0}], [{"node": "Guest Registration Info", "type": "main", "index": 0}], [{"node": "Guest Continue <PERSON>", "type": "main", "index": 0}], [{"node": "Get Roadmap Data", "type": "main", "index": 0}]]}, "Registration Handler": {"main": [[{"node": "Send Registration Response", "type": "main", "index": 0}]]}, "Create Registered User": {"main": [[{"node": "Registration Success Handler", "type": "main", "index": 0}]]}, "Registration Success Handler": {"main": [[{"node": "Send Registration Success", "type": "main", "index": 0}]]}, "Donation Processor": {"main": [[{"node": "Send Donation Response", "type": "main", "index": 0}]]}, "Investment Registration Prompt": {"main": [[{"node": "Send Investment Prompt", "type": "main", "index": 0}]]}, "Guest Mining Stats": {"main": [[{"node": "Send Guest Mining Stats", "type": "main", "index": 0}]]}, "Guest Investment Info": {"main": [[{"node": "Send Guest Investment Info", "type": "main", "index": 0}]]}, "Guest Registration Info": {"main": [[{"node": "Send Guest Registration Info", "type": "main", "index": 0}]]}, "Guest Continue Handler": {"main": [[{"node": "Send Guest Continue", "type": "main", "index": 0}]]}, "If role": {"main": [[{"node": "If ADMIN", "type": "main", "index": 0}], [{"node": "If INVESTOR", "type": "main", "index": 0}]]}, "If ADMIN": {"main": [[{"node": "Admin Action Placeholder", "type": "main", "index": 0}], [{"node": "If INVESTOR", "type": "main", "index": 0}]]}, "If MEMBER": {"main": [[{"node": "Callback Router", "type": "main", "index": 0}], [{"node": "If GUEST", "type": "main", "index": 0}]]}, "If INVESTOR": {"main": [[{"node": "Send Investment Details1", "type": "main", "index": 0}], [{"node": "If MEMBER", "type": "main", "index": 0}]]}, "Send Investment Details1": {"main": [[{"node": "Send Investment Details", "type": "main", "index": 0}]]}, "If GUEST": {"main": [[{"node": "Guest Mode Handler1", "type": "main", "index": 0}]]}, "Build Main Menu1": {"main": [[{"node": "Build Main Menu", "type": "main", "index": 0}]]}, "Guest Mode Handler1": {"main": [[{"node": "Callback Router", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "d7461df8-f263-47d4-8e26-f844588efef8", "meta": {"templateCredsSetupCompleted": true, "instanceId": "1f52f14222153875e5cd754160d07a91bc57a3dd7ee7266d4266eae0b531fc56"}, "id": "yXSgMYBuZvK8mqvY", "tags": [{"createdAt": "2025-08-06T12:34:09.665Z", "updatedAt": "2025-08-06T12:34:09.665Z", "id": "EKr6mdBmEMIyCP37", "name": "ELOH"}]}